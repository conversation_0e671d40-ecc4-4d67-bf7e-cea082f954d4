GOCMD=go
GOBUILD=$(GOCMD) build
GOBASE=$(shell pwd)
MAINFILE=$(GOBASE)/cmd/mdc-agent.go
BINARYFILE=$(GOBASE)/bin/mdc-agent
BUILD_DATE=$(shell date '+%Y-%m-%d %H:%M:%S')
COMMIT_HASH=$(shell git rev-parse --short HEAD || echo "GitNotFound")
all: build
build: 
	$(GOBUILD) -v -ldflags "-X \"main.DXMVersion=${COMMIT_HASH}\" -X \"main.DXMDate=$(BUILD_DATE)\"" -o $(BINARYFILE) $(MAINFILE)
clean:
	@rm -rf bin
