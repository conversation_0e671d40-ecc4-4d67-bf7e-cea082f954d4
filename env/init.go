package env

import (
	"log"

	"mdc-agent/config"
	"mdc-agent/library/logger"
	"mdc-agent/library/xtrabackup"
)

// Application Config
type Application struct {
	Mode  string `yaml:"mode"`
	Port  int    `yaml:"port"`
	Token string `yaml:"token"`
}

func Init(configPath ...string) {
	// 初始化配置
	err := config.Init(configPath...)
	if err != nil {
		log.Panicf("failed to init config, error=(%v)", err)
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("failed to init logger, error=(%v)", err)
	}
	logger.Init(&logConfig)

	// // 初始化 db
	// var dbConfig mysql.Config
	// err = config.Get("db", &dbConfig)
	// // dbConfig.AutoMigrate = true
	// if err != nil {
	// 	log.Panicf("failed to init mysql, error=(%v)", err)
	// }
	// mysql.Init(&dbConfig)

	// 获取xtrabackup配置
	var xtraConfig xtrabackup.Config
	err = config.Get("xtrabackup", &xtraConfig)
	if err != nil {
		log.Panicf("failed to init xtrabackup, error=(%v)", err)
	}
	xtrabackup.Init(&xtraConfig)

	// 获取app config
	// var appConfig Application
	// err = config.Get("application", &appConfig)
	// if err != nil {
	// 	log.Panicf("failed to init application, error=(%v)", err)
	// }

	// // 初始化 报警机器人
	// var hiConfig hi.Config
	// err = config.Get("hi", &hiConfig)
	// if err != nil {
	// 	log.Panicf("failed to init hi, error=(%v)", err)
	// }
	// hi.Init(&hiConfig)

	// // 初始化 noah
	// var noahConfig noah.Config
	// config.Get("noah", &noahConfig)
	// if err != nil {
	// 	log.Panicf("failed to init noah sdk, error=(%v)", err)
	// }
	// noah.Init(&noahConfig)

	// // 初始化 redis-agent
	// var agentConf ragent.Config
	// config.Get("redis_agent", &agentConf)
	// if err != nil {
	// 	log.Panicf("failed to init redis agent sdk, error=(%v)", err)
	// }
	// ragent.Init(&agentConf)

	// // 初始化 redisClient
	// var redisConfig redisc.Config
	// err = config.Get("redis", &redisConfig)
	// if err != nil {
	// 	log.Panicf("failed to init redis, error=(%v)", err)
	// }
	// redisc.Init(&redisConfig)

	// // 初始化 redis-xweb
	// var xwebConf xweb.Config
	// config.Get("redis_xweb", &xwebConf)
	// if err != nil {
	// 	log.Panicf("failed to init redis xweb, error=(%v)", err)
	// }
	// xweb.Init(&xwebConf)

	// // 初始化 fec
	// var fecConf fec.Config
	// config.Get("fec", &fecConf)
	// if err != nil {
	// 	log.Panicf("failed to init fec, error=(%v)", err)
	// }
	// fec.Init(&fecConf)

	// // 初始化 billing
	// var billingConfig billing.Config
	// config.Get("billing", &billingConfig)
	// logger.Debug("billing config: %+v", billingConfig)
	// if err != nil {
	// 	log.Panicf("failed to init billing sdk, error=(%v)", err)
	// }
	// billing.Init(&billingConfig)

	// // 初始化 reconcile
	// var rendererConf common.Config
	// config.Get("renderer", &rendererConf)
	// if err != nil {
	// 	log.Panicf("failed to init reconcile, error=(%v)", err)
	// }
	// rendererConf.Application = &common.Application{BNS: appConfig.BNS, Port: appConfig.Port, Token: appConfig.Token}
	// common.Init(&rendererConf)

	// // 初始化 sc
	// var scConfig apptree.Config
	// config.Get("cloud", &scConfig)
	// if err != nil {
	// 	log.Panicf("failed to init auth center, error=(%v)", err)
	// }
	// apptree.Init(&scConfig)

	// 自定义错误码
	// errc.Init()

}
