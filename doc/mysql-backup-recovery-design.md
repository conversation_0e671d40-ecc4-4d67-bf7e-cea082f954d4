# MySQL 数据备份、恢复和回滚方法集 - 设计方案

## 1. 项目背景

### 1.1 业务需求
- **应用场景**：生产环境，可对外售卖的商业云环境
- **数据规模**：T级数据，单个MySQL实例
- **性能要求**：备份需限速，减少对线上影响，支持随时停止重启
- **恢复要求**：不能覆盖原实例，需创建新实例进行恢复

### 1.2 现有痛点
- 目录结构不明确，功能杂糅在一个包里
- 缺乏库表级恢复、时间点恢复等精细化功能
- 缺少动态限速、暂停恢复等运维友好特性
- 没有双地域备份容灾机制

## 2. 整体架构设计

### 2.1 目录结构重构
```
mdc-agent/
├── library/                    # 新增工具库目录
│   ├── xtrabackup/            # XtraBackup 工具库
│   │   ├── manager.go         # 备份管理器
│   │   ├── config.go          # 配置定义
│   │   ├── limiter.go         # 限速控制
│   │   └── monitor.go         # 进度监控
│   ├── bos/                   # BOS 存储操作库
│   │   ├── client.go          # BOS 客户端封装
│   │   ├── uploader.go        # 并行上传器
│   │   ├── downloader.go      # 断点续传下载
│   │   └── dual_region.go     # 双地域上传
│   ├── binlog/                # Binlog 处理库
│   │   ├── parser.go          # Binlog 解析
│   │   ├── applier.go         # Binlog 应用
│   │   └── manager.go         # Binlog 管理
│   └── recovery/              # 恢复库
│       ├── manager.go         # 恢复管理器
│       ├── instance_recovery.go # 实例级恢复
│       ├── table_recovery.go  # 库表级恢复
│       └── pitr.go           # 时间点恢复
├── networker/worker/          # 保留现有 worker
└── conf/template/             # 保留现有模板
```

### 2.2 架构边界明确
- **mdc-agent 职责**：专注于数据备份、恢复，不负责 MySQL 实例部署
- **去除实例安装功能**：假设目标环境已有 MySQL 软件
- **保留最小控制功能**：仅在恢复过程中临时启停实例

## 3. 核心功能设计

### 3.1 备份功能增强

#### A. XtraBackup 管理器
```go
type XtraBackupManager struct {
    config    *Config
    limiter   *RateLimiter        // 动态限速控制
    monitor   *ProgressMonitor    // 进度监控
    pauseFile string              // 暂停控制文件
}

type Config struct {
    ThrottleRate    int64  // 限速 MB/s（可动态调整）
    CompressLevel   int    // 压缩级别 1-9
    ParallelThreads int    // 并行线程数
    ExtraOptions    string // 额外参数
}

// 核心方法
func (x *XtraBackupManager) FullBackup(ctx context.Context, opts *BackupOptions) (*BackupResult, error)
func (x *XtraBackupManager) IncrementalBackup(ctx context.Context, opts *BackupOptions) (*BackupResult, error)
func (x *XtraBackupManager) SetThrottleRate(rate int64) error  // 动态限速
func (x *XtraBackupManager) Pause() error                     // 暂停备份
func (x *XtraBackupManager) Resume() error                    // 恢复备份
func (x *XtraBackupManager) Stop() error                      // 停止并清理
```

#### B. 双地域 BOS 上传
```go
type DualRegionUploader struct {
    northClient *BOSClient  // 华北 BOS
    southClient *BOSClient  // 华南 BOS
}

func (d *DualRegionUploader) UploadToDualRegion(ctx context.Context, localPath, remotePath string) error {
    // 并行上传到华南、华北两个 BOS
    // 支持断点续传和完整性校验
}
```

### 3.2 恢复功能设计

#### A. 实例级恢复
```go
func HandleExecRestore(taskMsg *pb_agent.MdcAgentAsyncMsg) (*pb_server.ExecRestoreReport, error) {
    // 1. 恢复数据到目标目录
    err, dataPath := rsSnapshotData(targetPath, baseDataPath, incDataPath)
    
    // 2. 如果需要应用 binlog，启动临时实例
    if len(binlogDataPath) > 0 {
        tempPort := allocateTempPort()
        if err := startTempInstance(mysqlBaseDir, dataPath, tempPort); err != nil {
            return nil, err
        }
        defer stopTempInstance(mysqlBaseDir)
        
        // 应用 binlog 到指定时间点
        if err := applyBinlogFile(binlogDataPath, mysqlBaseDir, startTime, endTime); err != nil {
            return nil, err
        }
    }
    
    // 3. 返回数据目录路径
    return &pb_server.ExecRestoreReport{
        DataPath: dataPath,
        Status:   "completed",
    }, nil
}
```

#### B. 库表级恢复
```go
func HandleTableRecovery(taskMsg *pb_agent.MdcAgentAsyncMsg) (*pb_server.TableRecoveryReport, error) {
    // 1. 完整恢复数据到临时目录
    tempDataPath, err := recoverFullData(taskMsg)
    if err != nil {
        return nil, err
    }
    defer cleanupTempData(tempDataPath)
    
    // 2. 启动临时实例
    tempPort := allocateTempPort()
    if err := startTempInstance(mysqlBaseDir, tempDataPath, tempPort); err != nil {
        return nil, err
    }
    defer stopTempInstance(mysqlBaseDir)
    
    // 3. 导出指定库表
    tableFilter := taskMsg.GetTableRecovery().TableFilter
    exportPath, err := exportTables(mysqlBaseDir, tempPort, tableFilter)
    if err != nil {
        return nil, err
    }
    
    return &pb_server.TableRecoveryReport{
        ExportPath: exportPath,
        Status:     "completed",
    }, nil
}
```

### 3.3 gRPC 接口扩展

#### A. Protobuf 消息扩展
```protobuf
// 备份控制消息
message BackupControlMsg {
    string task_id = 1;
    BackupControlType control_type = 2;
    int64 throttle_rate = 3;  // 新的限速值 MB/s
}

enum BackupControlType {
    PAUSE = 0;
    RESUME = 1;
    STOP = 2;
    SET_THROTTLE = 3;
}

// 双地域备份配置
message DualRegionBackupConfig {
    string north_bos_path = 1;  // 华北 BOS 路径
    string south_bos_path = 2;  // 华南 BOS 路径
    bool enable_dual_upload = 3;
}

// 库表级恢复配置
message TableRecoveryConfig {
    repeated string databases = 1;    // 要恢复的数据库
    repeated string tables = 2;       // 要恢复的表 (db.table 格式)
    string export_format = 3;         // SQL|CSV 等格式
}
```

## 4. 关键特性

### 4.1 备份优化
- **动态限速**：运行时可调整备份速度
- **暂停/恢复**：支持备份过程的暂停和恢复
- **双地域上传**：同时上传到华南、华北 BOS
- **并行压缩**：多线程压缩提升效率
- **进度监控**：实时监控备份进度

### 4.2 恢复能力
- **实例级恢复**：完整恢复到指定目录
- **库表级恢复**：选择性恢复指定库表
- **时间点恢复**：基于 binlog 的精确 PITR
- **自动清理**：临时文件和实例的自动清理

### 4.3 架构优势
- **职责清晰**：agent 专注备份恢复，不负责实例部署
- **功能解耦**：library 目录实现功能模块化
- **向后兼容**：保持现有 gRPC 接口架构
- **易于扩展**：模块化设计便于功能扩展

## 5. 实施计划

### 第一阶段：基础重构（2-3周）
1. 创建 library 目录结构
2. 实现 XtraBackup 管理器（支持限速、暂停）
3. 实现双地域 BOS 上传器
4. 单元测试和集成测试

### 第二阶段：恢复功能（2-3周）
1. 实现实例级恢复优化
2. 实现库表级恢复功能
3. 实现时间点恢复（PITR）
4. 功能测试和性能优化

### 第三阶段：接口完善（1-2周）
1. 扩展 protobuf 定义
2. 实现新的 gRPC 接口
3. 完善错误处理和日志
4. 文档编写和部署测试

## 6. 技术要点

### 6.1 限速控制实现
```go
type RateLimiter struct {
    rate     int64           // 当前限速 MB/s
    ticker   *time.Ticker    // 控制频率
    mutex    sync.RWMutex    // 并发安全
}

func (r *RateLimiter) SetRate(rate int64) {
    r.mutex.Lock()
    defer r.mutex.Unlock()
    r.rate = rate
    // 动态调整 XtraBackup 的 --throttle 参数
}
```

### 6.2 双地域上传容错
```go
func (d *DualRegionUploader) UploadToDualRegion(ctx context.Context, localPath, remotePath string) error {
    var wg sync.WaitGroup
    errChan := make(chan error, 2)
    
    // 并行上传到两个地域
    wg.Add(2)
    go d.uploadToRegion(ctx, "north", localPath, remotePath, &wg, errChan)
    go d.uploadToRegion(ctx, "south", localPath, remotePath, &wg, errChan)
    
    wg.Wait()
    
    // 至少一个地域成功即可
    return d.checkUploadResults(errChan)
}
```

### 6.3 临时实例管理
```go
type TempInstanceManager struct {
    instances map[string]*TempInstance
    portPool  *PortPool
}

func (t *TempInstanceManager) StartTempInstance(dataPath string) (*TempInstance, error) {
    port := t.portPool.Allocate()
    instance := &TempInstance{
        ID:       generateID(),
        DataPath: dataPath,
        Port:     port,
    }
    
    // 启动实例但不安装
    if err := instance.Start(); err != nil {
        t.portPool.Release(port)
        return nil, err
    }
    
    t.instances[instance.ID] = instance
    return instance, nil
}
```

## 7. 总结

本设计方案在保持现有架构优势的基础上，明确了职责边界，增强了备份恢复能力，并提供了完整的库表级恢复功能。通过模块化设计和功能解耦，提升了系统的可维护性和扩展性，满足了生产环境对高可用、高性能备份恢复系统的需求。
