package common

import (
	"bytes"
	"errors"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"time"
)

//构造http client
var client = &http.Client{
	Transport: &http.Transport{
		DialContext: (&net.Dialer{
			//tcp握手时间5s，超时就error
			Timeout: 5 * time.Second,
		}).DialContext,
	},
}

//构建http request并发送
func DoHttpRequest(method string, url string, body []byte, header http.Header) ([]byte, error) {
	//构建request
	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		errStr := fmt.Sprintf("build request: %v, [%v]", request, err)
		return nil, errors.New(errStr)
	}
	if header != nil {
		request.Header = header
	}
	//发出request
	jsonResponseBody, err := Send(request)
	if err != nil {
		return nil, err
	}
	return jsonResponseBody, nil
}

func Send(request *http.Request) ([]byte, error) {
	response, err := client.Do(request)
	if err != nil {
		errStr := fmt.Sprintf("send http request: %v, [%v]", request, err)
		return nil, errors.New(errStr)
	}
	if response.StatusCode != http.StatusOK {
		errStr := fmt.Sprintf("http response.statuscode: [%v], [%v]", response.StatusCode, err)
		return nil, errors.New(errStr)
	}
	jsonResponseBody, err := ioutil.ReadAll(response.Body)
	if response.StatusCode != http.StatusOK {
		errStr := fmt.Sprintf("read response.body: [%v], [%v]", response.Body, err)
		return nil, errors.New(errStr)
	}

	return jsonResponseBody, nil
}
