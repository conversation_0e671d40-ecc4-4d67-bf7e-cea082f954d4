package common

import (
	"sync"

	pb_agent "dt-common/protobuf/mdc-agent"
)

// 声明用于接收server发来的消息
// agent端全局消息队列声明
var AgentMsgChan = make(chan *pb_agent.MdcAgentAsyncMsg, Config.ServerMsgChanBufferLength)
var AgentMsgChanIsClose int32 = 0

// bns解析的mdc server 的ip列表
var MdcServerIpList ServerConnInfo

type ServerConnInfo struct {
	IpList      []string
	RoundRobinQ []int
	Idx         int // 最后一次获取csIp的index+1
	RwLock      *sync.RWMutex
}

const (
	IoThread             = "Slave_IO_Running"
	SqlThread            = "Slave_SQL_Running"
	SlaveSQLRunningState = "Slave_SQL_Running_State"
	ExecutedGtidSet      = "Executed_Gtid_Set"
	MasterUUID           = "Master_UUID"
	MasterHost           = "Master_Host"
	MasterPort           = "Master_Port"
	MasterUser           = "Master_User"
	RelayMasterLogFile   = "Relay_Master_Log_File"
	MasterPassword       = "mysqlsync123"
	LocalHost            = "127.0.0.1"
	GtidSubset           = "gtid_subset"

	MAXPROCESSCOUNT     int = 3
	PasswordLen         int = 15
	FdbInnodbbufferPool     = "8G"
	FdbCharset              = "latin1"
	InitLsnPos              = "0"
	DbbkRsaPath             = "bin/.id_rsa_tmp_dbbk"
	InnobackupexBinPath     = "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/innobackupex"
	BceCMD                  = "bin/bcecmd"
	XbstreamBinPath         = "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/xbstream"
	//安装mysql配置模板目录
	LocalInstallConfDir = "conf/mysqlconf"
	ConfTeplPath        = "conf/template"
	//目标集群临时目录
	RemoteInstallDir = "/home/<USER>/opdir/mdc"

	BinlogPathName = "binlog"
	ThreadStatus   = "Yes"
	UnpackType     = "tar"
	XbstreamType   = "xbstream"
	DataDir        = "sourceData"
	//s3配置目录
	TapeConfig    = "/md_raid0mysql/checkdel/checkslap/.s3cfg_tape"
	TapeLimitRate = ********
)

// 用于模板替换的结构体
type TmplInstance struct {
	Host        string
	Port        int
	BaseDir     string
	DataDir     string
	TmpBaseDir  string
	ServerId    int64
	DownloadUrl string
}

// 恢复类型
type RsRole int32

const (
	Rd RsRole = iota
	Dba
)

// 上传到bos的前缀目录
const RestoreBosPrefix = "/RestorePartition/"

// 备份用户权限路径

const AccountBkDir = "accountBk/backup"
