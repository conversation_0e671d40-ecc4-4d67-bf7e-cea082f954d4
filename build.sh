#!/bin/bash
source ./go-env-build.sh

mkdir -p output output/status/mdc-agent output/logs && touch output/status/mdc-agent/status
make
cp supervise ./output/bin/supervise.mdc-agent
cp -r conf bin mdc-agent-control ./output

#下载备份恢复工具
wget -O mdc_tool_bin.tgz https://sqlonline.bcebos.hb-fsg.x3.dxmyun.com/mdc_tool_bin.tgz?authorization=bce-auth-v1/eb3c139a7d72444f845b8f8f2afda99c/2024-02-23T08%3A51%3A00Z/-1/host/926defe2fee66b9e96347858bc44351bb3debdd2b25b249ae48d50ef9aa7e693 && mkdir mdc_tool_bin && tar -zxf mdc_tool_bin.tgz -C mdc_tool_bin && rm -rf mdc_tool_bin.tgz
mv mdc_tool_bin/bin/* ./output/bin && mv  mdc_tool_bin/bin/.id_rsa_tmp_dbbk ./output/bin && rm -rf mdc_tool_bin/bin
