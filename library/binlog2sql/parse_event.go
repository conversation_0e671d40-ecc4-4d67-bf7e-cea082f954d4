package binlog2sql

import (
	"github.com/go-mysql-org/go-mysql/replication"
	"sync"
)

const (
	PARSE_PROCESS  = 0 //检查完每个事件后的流程，0:遇到rowsevent处理；1:跳过该事件；2:超过endtime，终止
	PARSE_CONTINUE = 1
	PARSE_STOP     = 2

	BINLOG_FILE_OFFSET = 4 //一个binlog文件的起始位点
)

//ParseEventsFromFile 处理binlog文件协程
func ParseEventsFromFile(ctx *Configs, wg *sync.WaitGroup) {
	defer wg.Done()
	var ctl int
	parser := replication.NewBinlogParser()
	for _, file := range ctx.FileList {
		parser.ParseFile(file, BINLOG_FILE_OFFSET, func(binlogEvent *replication.BinlogEvent) error {
			ctl = checkAndSendEvent(ctx, binlogEvent) //检测该事件并发送到eventChannel
			if ctl == PARSE_STOP || ctx.stopped {     //如果收到停止信号或者解析到超过时间段事件，停止解析
				parser.Stop()
			}
			return nil
		})
		parser.Reset()
		if ctl == PARSE_STOP { //接收到停止信号，退出，不继续处理后面的文件
			break
		}
	}
	ctx.StatusChan <- true
}

//checkAndSendEvent
//处理每一个event流程，判断event类型和时间，如果是rowevent则发送到EventChan
func checkAndSendEvent(ctx *Configs, binlogEvent *replication.BinlogEvent) int {
	var ctl int
	ctl = checkEvents(ctx, binlogEvent)
	//如果检查到该事件是rowsEvent，发送到channel
	if ctl == PARSE_PROCESS {
		ctx.EventChan <- binlogEvent
	}
	return ctl
}

//checkEvents
//子函数，用来判断event类型和时间，过滤rowsEvent
func checkEvents(ctx *Configs, event *replication.BinlogEvent) int {
	//如果当前事件早于设定时间段，跳过
	if event.Header.Timestamp < ctx.startTime {
		return PARSE_CONTINUE
	}
	//如果当前事件时间大于设定时间段，则之后的事件都不用处理
	if event.Header.Timestamp > ctx.endTime {
		return PARSE_STOP
	}
	//如果当前事件是rowsEvent则处理这些事件
	switch event.Header.EventType {
	case replication.WRITE_ROWS_EVENTv0,
		replication.WRITE_ROWS_EVENTv1,
		replication.WRITE_ROWS_EVENTv2,
		replication.UPDATE_ROWS_EVENTv0,
		replication.UPDATE_ROWS_EVENTv1,
		replication.UPDATE_ROWS_EVENTv2,
		replication.DELETE_ROWS_EVENTv0,
		replication.DELETE_ROWS_EVENTv1,
		replication.DELETE_ROWS_EVENTv2:
		goto CHECK
	default:
		return PARSE_CONTINUE
	}
CHECK:
	return PARSE_PROCESS

}
