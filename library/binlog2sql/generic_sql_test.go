package binlog2sql

import (
	"testing"
	
	"github.com/go-mysql-org/go-mysql/replication"
	"github.com/smartystreets/goconvey/convey"
	
)

var (
	insertEvent *replication.BinlogEvent
	deleteEvent *replication.BinlogEvent
	updateEvent *replication.BinlogEvent
)

func init() {
	BinlogParse := replication.NewBinlogParser()
	BinlogParse.ParseFile(InsertFile, 4, func(e *replication.BinlogEvent) error {
		if e.Header.EventType == replication.WRITE_ROWS_EVENTv2 ||
			e.Header.EventType == replication.WRITE_ROWS_EVENTv1 ||
			e.Header.EventType == replication.WRITE_ROWS_EVENTv0 {
			insertEvent = e
		}
		return nil
	})

	BinlogParse.Reset()
	BinlogParse.ParseFile(DeleteFile, 4, func(e *replication.BinlogEvent) error {
		if e.Header.EventType == replication.DELETE_ROWS_EVENTv2 ||
			e.Header.EventType == replication.DELETE_ROWS_EVENTv1 ||
			e.Header.EventType == replication.DELETE_ROWS_EVENTv0 {
			deleteEvent = e
		}
		return nil
	})

	BinlogParse.Reset()
	BinlogParse.ParseFile(UpdateFile, 4, func(e *replication.BinlogEvent) error {
		if e.Header.EventType == replication.UPDATE_ROWS_EVENTv2 ||
			e.Header.EventType == replication.UPDATE_ROWS_EVENTv1 ||
			e.Header.EventType == replication.UPDATE_ROWS_EVENTv0 {
			updateEvent = e
		}
		return nil
	})
}

func TestGenericInsertSQLFromOneRowEvent(t *testing.T) {
	convey.Convey("Insert语句测试", t, func() {
		insertSql, err := GenericSQLsFromOneRowEvent(cfg, insertEvent)
		convey.So(err, convey.ShouldBeNil)
		convey.So(len(insertSql), convey.ShouldEqual, 1)
		convey.So(insertSql[0], convey.ShouldEqual, InsertSQL)
	})
}

func TestGenericDeleteSQLFromOneRowEvent(t *testing.T) {
	convey.Convey("Delete语句测试", t, func() {
		deleteSql, err := GenericSQLsFromOneRowEvent(cfg, deleteEvent)
		convey.So(err, convey.ShouldBeNil)
		convey.So(len(deleteSql), convey.ShouldEqual, 1)
		convey.So(deleteSql[0], convey.ShouldEqual, DeleteSQL)
	})
}

func TestGenericUpdateSQLFromOneRowEvent(t *testing.T) {
	convey.Convey("Update语句测试", t, func() {
		updateSql, err := GenericSQLsFromOneRowEvent(cfg, updateEvent)
		convey.So(err, convey.ShouldBeNil)
		convey.So(len(updateSql), convey.ShouldEqual, 1)
		convey.So(updateSql[0], convey.ShouldEqual, UpdateSQL)
	})
}
