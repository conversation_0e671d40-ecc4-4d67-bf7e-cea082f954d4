package binlog2sql

import (
	"database/sql"

	//mysql driver
	_ "github.com/go-sql-driver/mysql"

	"mdc-agent/library/logger"
)

func createMysqlConnection(url string) (*sql.DB, error) {
	//创建mysql连接
	db, err := sql.Open("mysql", url)
	if err != nil {
		logger.Error("ErrorMsg=[connect to mysql error.] CalledError=[%v] MysqlUrl=[%s]", err, url)
		if db != nil {
			db.Close()
		}
		return nil, err
	}

	err = db.Ping()
	if err != nil {
		logger.Error("ErrorMsg=[ping mysql timeout.] CalledError=[%v] MysqlUrl=[%s]", err, url)
		if db != nil {
			db.Close()
		}
		return nil, err
	}
	return db, err
}

// 工具函数，判断slice中有没有某字符串
func containsString(stringSlice []string, target string) bool {
	for _, str := range stringSlice {
		if str == target {
			return true
		}
	}
	return false
}

// 比较两个slice是否相等
func compareSlice(slice1 []byte, slice2 []byte) bool {
	if len(slice1) != len(slice2) {
		return false
	}
	for index, val := range slice1 {
		if val != slice2[index] {
			return false
		}
	}
	return true
}
