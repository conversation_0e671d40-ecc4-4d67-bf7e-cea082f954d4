package binlog2sql

import (
	"testing"

	"github.com/smartystreets/goconvey/convey"
)

func TestGetTable(t *testing.T) {
	primaryKeys := []string{"ID"}
	columnTypes := []string{"int(11)", "varchar(12)", "float", "double", "decimal(10,0)", "datetime", "blob", "blob"}
	columnNames := []string{"ID", "str", "flo", "dou", "deci", "dat", "txt", "blo"}
	convey.Convey("GetTableInfo 正常测试", t, func() {
		tableinfo, err := GetTbInfo(cfg, "binlogtosqltest", "deleteTest")
		convey.So(err, convey.ShouldBeNil)
		PrimarykeyResult := tableinfo.PrimaryKey()
		convey.So(PrimarykeyResult, convey.ShouldResemble, primaryKeys)
		columnResults := tableinfo.Columns()
		for index, column := range columnResults {
			convey.So(column.ColumnName(), convey.ShouldEqual, columnNames[index])
			convey.So(column.ColumnType(), convey.ShouldEqual, columnTypes[index])
		}
	})
}

func TestGetDropTable(t *testing.T) {
	convey.Convey("DropTable 测试", t, func() {
		tableinfo, err := GetTbInfo(cfg, "binlogtosqltest", "dropTable")
		convey.So(tableinfo, convey.ShouldBeNil)
		convey.So(err.Error(), convey.ShouldContainSubstring, "dropped")
	})
}
