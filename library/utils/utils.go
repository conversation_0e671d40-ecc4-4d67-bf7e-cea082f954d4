package utils

import (
	"bytes"
	"fmt"
	"os/exec"
	"strings"
)

// 执行本地shell命令
func ShellExec(command string, args ...string) (string, error) {
	cmd := exec.Command(command, args...)
	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout // 标准输出
	cmd.Stderr = &stderr // 标准错误
	err := cmd.Run()
	outStr, errStr := stdout.String(), stderr.String()
	if err != nil {
		return "", fmt.<PERSON><PERSON>rf("%s", errStr)
	}

	if outStr != "" {
		return outStr, nil
	}

	return "", fmt.Errorf("nil output")
}

func FormatClusterName(clusterName string) string {
	return strings.Replace(clusterName, "_", "-", -1)
}

func FormatClusterNameToUnderline(clusterName string) string {
	return strings.Replace(clusterName, "-", "_", -1)
}
