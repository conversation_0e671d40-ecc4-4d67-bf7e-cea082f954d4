package utils

import "testing"

func TestGetLocalIP(t *testing.T) {
	// type args struct {}
	tests := []struct {
		name   string
		before func()
		// args    args
		wantErr bool
		expect  func(t *testing.T, ip string)
	}{
		{
			name:   "test1",
			before: func() {},
			// args: args{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			ip, err := GetLocalIP()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLocalIP() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, ip)
			}
		})
	}
}
