package utils

import (
	"fmt"
	"io"
	"os"
)

// 重写文件
func RewriteFile(filePath string, content string) error {
	file, err := os.OpenFile(filePath, os.O_WRONLY, os.ModeAppend)
	if err != nil {
		return err
	}
	defer file.Close()
	err = file.Truncate(0)
	if err != nil {
		return err
	}
	_, err = file.WriteString(content)
	return err
}

// 复制文件
func CopyFile(sourcePath string, targetPath string, overlay bool) error {
	// 检查源文件是否存在
	_, err := os.Stat(sourcePath)
	if err != nil && os.IsNotExist(err) {
		return fmt.Errorf("source file %s not exists", sourcePath)
	}
	srcReader, err := os.Open(sourcePath)
	if err != nil {
		return err
	}

	// 检查目标文件是否存在
	var dstWriter *os.File
	_, err = os.Stat(targetPath)
	if err != nil && os.IsNotExist(err) {
		dstWriter, err = os.Create(targetPath)
		if err != nil {
			return err
		}
	} else if err == nil && !overlay {
		return fmt.Errorf("target file %s already exists", targetPath)
	} else {
		dstWriter, err = os.OpenFile(targetPath, os.O_WRONLY, os.ModeAppend)
		dstWriter.Truncate(0)
		if err != nil {
			return err
		}
	}

	_, err = io.Copy(dstWriter, srcReader)
	return err
}
