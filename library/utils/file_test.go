package utils

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

// 单测：复制文件
func TestCopyFile(t *testing.T) {
	Convey("CreatePolicy", t, func() {
		sourceFile := "/Users/<USER>/work/code/dxm/siod-cloud/dt-common/utils/test/source.txt"
		targetFile := "/Users/<USER>/work/code/dxm/siod-cloud/dt-common/utils/test/target.txt"
		//初始化参数
		err := CopyFile(sourceFile, targetFile, true)
		So(err, ShouldBeNil)
	})
}
