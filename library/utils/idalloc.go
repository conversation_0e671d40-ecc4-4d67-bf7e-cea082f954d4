package utils

import (
	"math"
	"sync"
	"time"
)

/*
* 0 1                                               42           52             64
* +-+-----------------------------------------------+------------+---------------+
* | | timestamp(ms)                                 | workerId   | sequence      |
* +-+-----------------------------------------------+------------+---------------+
* |0| 0000000000 0000000000 0000000000 0000000000 0 | 0000000000 | 0000000000 00 |
* +-+-----------------------------------------------+------------+---------------+
*
* 0. 保留，置为0
* 1. 41位时间截(毫秒级)
* 2. 10位数据机器位
* 3. 12位序列，毫秒内的计数，同一机器，同一时间截并发4096个序号
 */
const (
	twEpoch        = int64(1483228800000)             //开始时间截 (2017-01-01)
	workerIdBits   = uint(10)                         //机器id所占的位数
	sequenceBits   = uint(12)                         //序列所占的位数
	workerIdMax    = int64(-1 ^ (-1 << workerIdBits)) //支持的最大机器id数量
	sequenceMask   = int64(-1 ^ (-1 << sequenceBits)) //支持的最大序列数量
	workerIdShift  = sequenceBits                     //机器id左移位数
	timestampShift = sequenceBits + workerIdBits      //时间戳左移位数
)

var IDAlloc *SnowFlake

func init() {
	IDAlloc = NewSnowFlake(0)
}

type SnowFlake struct {
	mu        sync.Mutex
	timestamp int64
	workerId  int64
	sequence  int64
}

// NewSnowFlake returns a new snowflake to generates id
func NewSnowFlake(workerId int64) *SnowFlake {
	if workerId < 0 || workerId > workerIdMax {
		workerId = 0
	}
	return &SnowFlake{
		timestamp: 0,
		workerId:  workerId,
		sequence:  0,
	}
}

// Generate 雪花算法生成Int64
func (s *SnowFlake) Generate() int64 {
	s.mu.Lock()
	now := time.Now().UnixNano() / 1000000
	if now == s.timestamp {
		s.sequence = (s.sequence + 1) & sequenceMask
		if s.sequence == 0 {
			for now <= s.timestamp {
				now = time.Now().UnixNano() / 1000000
			}
		}
	} else {
		s.sequence = 0
		s.timestamp = now
	}
	r := (now-twEpoch)<<timestampShift | (s.workerId << workerIdShift) | (s.sequence)
	s.mu.Unlock()
	return r
}

// 根据唯一id的最小值和要求得到数字的位数 返回生成后的唯一id
//
//	eg: uniqueId = common.UniqueInt64(9)
//	    得到一个9位数
func uniqueInt64(digitNum int) int64 {
	// 值溢出保护
	if digitNum <= 0 {
		return -1
	}
	// 得到基础数
	baseDigit := IDAlloc.GenerateWithoutWorkerId()

	// 得到大于要求位数的最小10的幂
	max := int64(math.Pow(10, float64(digitNum)))

	// 得到要求位数的最大10的幂
	min := max / 10

	// 如果基础数小于最小值,则加上传入的最小值后直接返回
	if 1 > baseDigit/min {
		return baseDigit + min
	}

	// 如果基础数在要求返回数的最高位上为0, 则取余加上min后再返回
	if min > (baseDigit % max) {
		return baseDigit%max + min
	}

	return baseDigit % max
}

// UniqueInt64 因为前端(xdb-plus)最大只能接收9位数的id，所以这里暂时写死为9位
func UniqueInt64() int64 {
	return uniqueInt64(9)
}

// GenerateWithoutWorkerId SnowFlake去掉workerId的版本
func (s *SnowFlake) GenerateWithoutWorkerId() int64 {
	s.mu.Lock()
	now := time.Now().UnixNano() / 1000000
	if now == s.timestamp {
		s.sequence = (s.sequence + 1) & sequenceMask
		if s.sequence == 0 {
			for now <= s.timestamp {
				now = time.Now().UnixNano() / 1000000
			}
		}
	} else {
		s.sequence = 0
		s.timestamp = now
	}
	r := (now-twEpoch)<<sequenceBits | (s.sequence)
	s.mu.Unlock()
	return r
}
