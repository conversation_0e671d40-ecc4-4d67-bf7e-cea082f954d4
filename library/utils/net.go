package utils

import (
	"fmt"
	"net"
)

func GetLocalIP() (string, error) {
	localIP := ""
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "", err
	}
	for _, address := range addrs {
		if ipnet, ok := address.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				localIP = ipnet.IP.String()
				break
			}
		}
	}
	if localIP == "" {
		return "", fmt.Errorf("failed to get localIP")
	}

	return localIP, nil
}
