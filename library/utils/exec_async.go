package utils

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"sync"
	"syscall"
	"time"

	"mdc-agent/library/logger"
)

// Process 进程管理
type Process struct {
	cmd       *exec.Cmd          // 进程对象
	cancel    context.CancelFunc // 取消函数
	cmdStr    string             // 执行命令字符串
	startTime time.Time          // 启动时间
	done      chan error         // 进程完成通道
	mutex     sync.RWMutex       // 读写锁
}

// Stop 停止进程
func (p *Process) Stop() error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	if p.cancel == nil {
		return fmt.Errorf("process not started or already stopped")
	}

	// 1. 首先尝试优雅停止
	p.cancel()

	// 2. 等待进程优雅退出
	select {
	case <-p.done:
		logger.Info("Process stopped gracefully")
		return nil
	case <-time.After(10 * time.Second):
		// 3. 超时后强制 kill
		if p.cmd != nil && p.cmd.Process != nil {
			logger.Warn("Process did not stop gracefully, force killing")
			err := p.cmd.Process.Kill()
			if err != nil {
				return fmt.Errorf("failed to force kill process: %w", err)
			}
			// 等待进程真正结束
			<-p.done
		}
		return nil
	}
}

// GetPID 获取进程 ID
func (p *Process) GetPID() int {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	if p.cmd != nil && p.cmd.Process != nil {
		return p.cmd.Process.Pid
	}
	return 0
}

// IsRunning 检查进程是否正在运行
func (p *Process) IsRunning() bool {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	if p.cmd == nil || p.cmd.Process == nil {
		return false
	}

	// 发送信号 0 检查进程是否存在
	err := p.cmd.Process.Signal(syscall.Signal(0))
	return err == nil
}

// Wait 等待进程完成，返回进程的退出错误
func (p *Process) Wait() error {
	return <-p.done
}

// WaitWithTimeout 等待进程完成，带超时控制
func (p *Process) WaitWithTimeout(timeout time.Duration) error {
	select {
	case err := <-p.done:
		return err
	case <-time.After(timeout):
		return fmt.Errorf("process wait timeout after %v", timeout)
	}
}

// GetStartTime 获取进程启动时间
func (p *Process) GetStartTime() time.Time {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.startTime
}

// GetCommand 获取执行的命令字符串
func (p *Process) GetCommand() string {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.cmdStr
}

// ExecCommandAsync 异步执行命令，返回 Process 对象用于进程管理
// 会在内部创建可取消的 context，用户传入的 context 作为父 context
func ExecCommandAsync(shell *Shell) (*Process, error) {
	// 参数验证
	parentCtx := shell.Context
	if parentCtx == nil {
		parentCtx = context.Background()
	}

	// 创建可取消的 context
	ctx, cancel := context.WithCancel(parentCtx)

	// 创建命令
	cmd := exec.CommandContext(ctx, shell.Command, shell.Args...)

	// 启动进程（异步）
	err := cmd.Start()
	if err != nil {
		cancel() // 启动失败时取消 context
		return nil, fmt.Errorf("failed to start command: %w", err)
	}

	// 创建 Process 对象
	process := &Process{
		cmd:       cmd,
		cancel:    cancel,
		cmdStr:    strings.Join(append([]string{shell.Command}, shell.Args...), " "),
		startTime: time.Now(),
		done:      make(chan error, 1),
	}

	// 启动 goroutine 等待进程完成
	go func() {
		defer close(process.done)
		err := process.cmd.Wait()
		process.done <- err
	}()

	return process, nil
}
