package xtrabackup

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"

	"mdc-agent/library/logger"
)

const (
	timeout = 5 * time.Second
)

// 返回带有超时时间的context，超时时间不指定使用配置文件中的timeout，默认10s
func ContextWithTimeout(seconds ...time.Duration) (context.Context, context.CancelFunc) {
	t := timeout
	if len(seconds) != 0 {
		t = seconds[0] * time.Second
	}

	return context.WithTimeout(context.Background(), t)
}

// 使用socket方式创建MySQL简单连接
func conn(user, password, socket string) (*sql.DB, error) {
	// 连接字符串：用户名:密码@unix(UnixSocket路径)/ (不指定数据库)
	dsn := fmt.Sprintf("%s:%s@unix(%s)/", user, password, socket)

	// 打开数据库连接
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, err
	}

	// 测试连接
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database, error=(%v)", err)
	}
	logger.Info("connected to mysql via unix socket successfully!")

	return db, nil
}

// 执行SHOW VARIABLES查询语句
func showVariable(db *sql.DB, key string) (string, error) {
	// 注意：SHOW 语句不支持占位符，需要直接拼接字符串
	query := fmt.Sprintf("SHOW VARIABLES LIKE '%s'", key)

	var name, value string
	ctx, cancel := ContextWithTimeout()
	err := db.QueryRowContext(ctx, query).Scan(&name, &value)
	cancel()
	if err != nil {
		return "", err
	}

	return value, nil
}

// ===================================
//			    并行复制
// ===================================

// disableParallelReplication 关闭并行复制
func DisableParallelReplication(options *BackupOptions) (int, error) {
	// 建立连接
	db, err := conn(options.User, options.Password, options.Socket)
	if err != nil {
		return -1, err
	}
	defer db.Close()

	// 检查是否开启了并行复制
	val, err := showVariable(db, "slave_parallel_workers")
	if err != nil {
		return -1, err
	}
	if val == "0" {
		return 0, nil
	}
	// 原始配置返回上层便于恢复
	workers, _ := strconv.Atoi(val)

	// 关闭并行复制
	sqls := []string{
		"stop slave;",
		"set global slave_parallel_workers=0;",
		"set global slave_preserve_commit_order=0;",
		"start slave;",
	}
	ctx, cancel := ContextWithTimeout()
	_, err = db.ExecContext(ctx, strings.Join(sqls, ""))
	cancel()
	if err != nil {
		return 0, err
	}
	logger.Info("slave parallel workers set to 0 for backup")

	return workers, nil
}

// 启用并行复制
func EnableParallelReplication(options *BackupOptions, workers int) error {
	// 如果workers为0，说明不需要开启，直接返回
	if workers == 0 {
		return nil
	}

	// 建立连接
	db, err := conn(options.User, options.Password, options.Socket)
	if err != nil {
		return err
	}
	defer db.Close()

	// 关闭并行复制
	sqls := []string{
		"stop slave;",
		fmt.Sprintf("set global slave_parallel_workers=%d;", workers),
		"set global slave_preserve_commit_order=1;",
		"start slave;",
	}
	ctx, cancel := ContextWithTimeout()
	_, err = db.ExecContext(ctx, strings.Join(sqls, ""))
	cancel()
	if err != nil {
		return err
	}
	logger.Info("slave parallel workers set to  for backup")

	return nil
}
