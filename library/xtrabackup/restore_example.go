package xtrabackup

import (
	"fmt"
	"log"
	"mdc-agent/library/xtrabackup"
)

func ExampleRestore() {
	// 1. 初始化 xtrabackup 配置
	config := &Config{
		BinPath:   "/usr/bin",                    // xtrabackup 二进制文件路径
		TmpPath:   "/tmp/xtrabackup",             // 临时文件存放路径
		LogPath:   "/var/log/xtrabackup",         // 日志存放路径
		UseMemory: "1G",                          // 使用内存限制
	}

	if err := Init(config); err != nil {
		log.Fatalf("Failed to initialize xtrabackup: %v", err)
	}

	// 2. 配置恢复选项
	restoreOptions := &RestoreOptions{
		FullBackupFile: "mysql@192.168.1.100:/backup/full-backup.xbstream",
		IncrementalFiles: []string{
			"mysql@192.168.1.100:/backup/incr-1-backup.xbstream",
			"mysql@192.168.1.100:/backup/incr-2-backup.xbstream",
		},
		TargetDataDir:   "/var/lib/mysql/data",   // MySQL 数据目录
		UseMemory:       "2G",                    // 可选：覆盖默认内存设置
		ParallelThreads: 8,                       // 可选：并行线程数
	}

	// 3. 执行恢复
	fmt.Println("Starting MySQL restore...")
	result, err := Restore(restoreOptions)
	if err != nil {
		log.Fatalf("Restore failed: %v", err)
	}

	// 4. 输出恢复结果
	if result.Success {
		fmt.Printf("Restore completed successfully!\n")
		fmt.Printf("Duration: %v\n", result.Duration)
		fmt.Printf("Data restored to: %s\n", result.DataPath)
	} else {
		fmt.Printf("Restore failed: %s\n", result.ErrorMsg)
	}
}

// 仅全量备份恢复示例
func restoreFullBackupOnly() {
	config := &xtrabackup.Config{
		BinPath:   "/usr/bin",
		TmpPath:   "/tmp/xtrabackup",
		LogPath:   "/var/log/xtrabackup",
		UseMemory: "1G",
	}

	if err := xtrabackup.Init(config); err != nil {
		log.Fatalf("Failed to initialize xtrabackup: %v", err)
	}

	restoreOptions := &xtrabackup.RestoreOptions{
		FullBackupFile:  "mysql@192.168.1.100:/backup/full-backup.xbstream",
		TargetDataDir:   "/var/lib/mysql/data",
		// IncrementalFiles 为空，表示只恢复全量备份
	}

	result, err := xtrabackup.Restore(restoreOptions)
	if err != nil {
		log.Fatalf("Restore failed: %v", err)
	}

	if result.Success {
		fmt.Printf("Full backup restore completed in %v\n", result.Duration)
	}
}
