/**
 *	xtrabackup 备份
 *	根据给定的Backup Options，启动对应集群在本机的实例备份
 *	- 同一个集群在本机只能跑一个备份任务，不论是全备还是增倍，当一个备份任务没跑完时，后来的备份任务都不能启动
 *  - 支持ssh远程流式传输
 *  - 支持超时控制、重试，支持主动停止
 *  - 备份任务完成后返回备份结果，包括：启动时间、完成时间、LSN信息等
 */
package xtrabackup

import (
	"bufio"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"mdc-agent/library/logger"
	"mdc-agent/library/utils"
)

var (
	lockMap sync.Map // 同一个集群同时只能在本机跑一个备份
)

// BackupType 备份类型
type BackupType string

const (
	BackupTypeFull        BackupType = "full" // 全量备份
	BackupTypeIncremental BackupType = "incr" // 增量备份
)

// BackupOptions 备份选项
type BackupOptions struct {
	Cluster     string // 集群，用于加锁
	DefaultFile string // 默认配置文件路径
	Socket      string // socket 文件路径
	User        string // MySQL 用户名
	Password    string // MySQL 密码

	CompressThreads      int    // 压缩线程数
	UseMemory            string // 使用内存
	Parallel             int    // 并行线程数
	Throttle             int    // 限速
	KillLongQueryType    string // 杀死长查询类型
	KillLongQueryTimeout int    // 杀死长查询超时时间

	Type        BackupType // 备份类型
	LSNPosition string     // LSN 位置（增量备份用）
	TargetDir   string     // 目标目录
	Stream      bool       // 流式备份
	RemoteHost  string     // 远程主机
	RemotePath  string     // 远程路径

	logDir      string // 备份任务的日志目录
	bakFilePath string // 备份文件的完整路径
}

// BackupResult 备份结果
type BackupResult struct {
	FilePath  string    // 文件路径
	StartTime time.Time // 开始时间
	EndTime   time.Time // 结束时间
	LSNFrom   string    // 起始 LSN
	LSNTo     string    // 结束 LSN
}

// 备份参数检查 & 初始化
func backupArgsValidate(options *BackupOptions) error {
	timestamp := time.Now().Format("2006-01-02-15-04-05")
	if options.Cluster == "" {
		return fmt.Errorf("cluster name can not be empty")
	}
	if options.DefaultFile == "" {
		return fmt.Errorf("default file can not be empty")
	}
	if options.Socket == "" {
		return fmt.Errorf("socket can not be empty")
	}
	if options.User == "" || options.Password == "" {
		return fmt.Errorf("user or password can not be empty")
	}

	// 远程传输
	if options.Stream {
		// 需要指明宿主机和目录
		if options.RemoteHost == "" {
			return fmt.Errorf("remote host can not be empty when remote transfer")
		}
		if options.RemotePath == "" {
			return fmt.Errorf("remote path can not be empty when remote transfer")
		}
		options.bakFilePath = fmt.Sprintf("%s/%s/backup-%s-%s.xbstream", options.RemotePath, options.Cluster, options.Type, timestamp)
	}
	// 增量备份需要指定LSN点位
	if options.Type == BackupTypeIncremental && options.LSNPosition == "" {
		return fmt.Errorf("lsn position can not be empty when incremental backup")
	}
	// 默认长事务处理参数
	if options.KillLongQueryType == "" {
		options.KillLongQueryType = "select"
	}
	if options.KillLongQueryTimeout <= 0 {
		options.KillLongQueryTimeout = 20
	}
	if options.Throttle <= 0 {
		options.Throttle = 500
	}
	if options.UseMemory == "" {
		options.UseMemory = useMemory
	}

	// 本次备份任务的文件目录
	options.TargetDir = fmt.Sprintf("%s/%s", logPath, options.Cluster)
	options.logDir = fmt.Sprintf("%s/%s-%s", options.TargetDir, options.Type, timestamp)

	return nil
}

// ./xtrabackup --defaults-file=/home/<USER>/mysql/etc/my.cnf \
// --user=root --password='_Y5%C2wncJC6b^frHdiEKw*kn05VNN' \
// --socket=/home/<USER>/mysql/tmp/mysql.sock \
// --backup \
// --compress \
// --compress-threads=4 \
// --parallel=16 \
// --slave-info \
// --lock-ddl-per-table \
// --stream=xbstream \
// --incremental-lsn=21075002180 \
// --extra-lsndir=/home/<USER>/backups/backup-incr-21075002180-lsn \
// 2> /home/<USER>/backups/backup-incr-$(date +'%F') \
// | ssh work@10.32.162.80 "cat > /home/<USER>/backups/backup-incr-21075002180.xbstream"

// backupExecCommand 执行备份
func backupExecCommand(options *BackupOptions) error {
	// 创建目录
	if err := os.MkdirAll(options.logDir, 0755); err != nil {
		return fmt.Errorf("failed to create log dir: %w", err)
	}

	// 构建 Shell 对象
	shell := &utils.Shell{
		Command: binXtrabackup,
		Args:    []string{},
	}

	// 基础参数
	shell.Args = append(shell.Args, fmt.Sprintf("--defaults-file=%s", options.DefaultFile))
	shell.Args = append(shell.Args, fmt.Sprintf("--socket=%s", options.Socket))
	shell.Args = append(shell.Args, fmt.Sprintf("--user=%s", options.User))
	shell.Args = append(shell.Args, fmt.Sprintf("--password=%s", options.Password))
	shell.Args = append(shell.Args, "--backup")
	shell.Args = append(shell.Args, fmt.Sprintf("--target-dir=%s", options.TargetDir))
	shell.Args = append(shell.Args, fmt.Sprintf("--extra-lsndir=%s", options.logDir))

	// 性能参数
	shell.Args = append(shell.Args, "--slave-info")
	shell.Args = append(shell.Args, "--lock-ddl-per-table")
	shell.Args = append(shell.Args, "--compress", fmt.Sprintf("--compress-threads=%d", options.CompressThreads))
	shell.Args = append(shell.Args, fmt.Sprintf("--kill-long-query-type=%s", options.KillLongQueryType))
	shell.Args = append(shell.Args, fmt.Sprintf("--kill-long-queries-timeout=%d", options.KillLongQueryTimeout))
	shell.Args = append(shell.Args, fmt.Sprintf("--throttle=%d", options.Throttle))

	// 增备
	if options.Type == BackupTypeIncremental {
		shell.Args = append(shell.Args, fmt.Sprintf("--incremental-lsn=%s", options.LSNPosition))
	}

	// 流式传输
	if options.Stream {
		shell.Args = append(shell.Args, "--stream=xbstream")
	}

	// 日志文件留存，日志文件命名规范：集群名/backup-备份类型-备份时间.log
	shell.Args = append(shell.Args, fmt.Sprintf("2> %s/xtrabackup.log", options.logDir))

	// 远程传输
	if options.Stream {
		sshCmd := fmt.Sprintf("ssh %s 'cat > %s'", options.RemoteHost, options.bakFilePath)
		shell.Args = append(shell.Args, "|", sshCmd)
		shell.Args = append([]string{binXtrabackup}, shell.Args...)
		shell.Args = []string{"-c", strings.Join(shell.Args, " ")}
		shell.Command = "bash"
	}

	// 为获取process，异步启动子进程
	process, err := utils.ExecCommandAsync(shell)
	if err != nil {
		return fmt.Errorf("failed to start backup process: %w", err)
	}
	logger.Debug("process cmd: %s", process.GetCommand())

	// 更新lockMap
	lockMap.Store(options.Cluster, process)

	// 等待进程完成
	err = process.Wait()
	if err != nil {
		logger.Error("failed to run backup process for cluster %s, error=(%v)", options.Cluster, err)
		return err
	}
	logger.Info("succeed to run backup process for cluster %s", options.Cluster)

	return nil
}

// uuid = 1202827a-8d4a-11f0-aab8-fa2700000477
// name =
// tool_name = xtrabackup
// tool_command = --defaults-file=/home/<USER>/mysql/etc/my.cnf --user=root --password=... --socket=/home/<USER>/mysql/tmp/mysql.sock --backup --compress --compress-threads=4 --parallel=16 --slave-info --lock-ddl-per-table --stream=xbstream --incremental-lsn=21075002180 --extra-lsndir=/home/<USER>/backups/backup-incr-21075002180-lsn
// tool_version = 2.4.15
// ibbackup_version = 2.4.15
// server_version = 5.7.25-log
// start_time = 2025-09-09 14:56:07
// end_time = 2025-09-09 14:56:11
// lock_time = 0
// binlog_pos = filename 'mysql-bin.000031', position '219416882', GTID of the last change 'c9d897f9-fe94-11ed-9852-fa2700000477:1-25239555'
// innodb_from_lsn = 21075002180
// innodb_to_lsn = 21101453281
// partial = N
// incremental = Y
// format = xbstream
// compact = N
// compressed = compressed
// encrypted = N

// 整理备份结果
func backupResult(lsnDir string) (*BackupResult, error) {
	// 读文件
	file, err := os.Open(filepath.Join(lsnDir, "xtrabackup_info"))
	if err != nil {
		return nil, err
	}
	defer file.Close()

	// 逐行解析
	info := &BackupResult{}
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		if scanner.Text() == "" {
			continue
		}

		line := scanner.Text()
		switch true {
		case strings.HasPrefix(line, "start_time ="):
			info.StartTime, err = time.Parse("2006-01-02 15:04:05", strings.Split(line, " = ")[1])
			if err != nil {
				return nil, err
			}
		case strings.HasPrefix(line, "end_time ="):
			info.EndTime, err = time.Parse("2006-01-02 15:04:05", strings.Split(line, " = ")[1])
			if err != nil {
				return nil, err
			}
		case strings.HasPrefix(line, "innodb_from_lsn ="):
			info.LSNFrom = strings.Split(line, " = ")[1]
		case strings.HasPrefix(line, "innodb_to_lsn ="):
			info.LSNTo = strings.Split(line, " = ")[1]
		}
	}

	return info, nil
}

// 启动实例备份，同步任务
func StartBackup(options *BackupOptions) (*BackupResult, error) {
	// 1、参数校验&初始化
	err := backupArgsValidate(options)
	if err != nil {
		return nil, err
	}

	// 2、加锁，同一个集群同时只能跑一个备份
	// 临时加个空锁，xtrabackup子进程启动后更新锁的详细内容
	if _, loaded := lockMap.LoadOrStore(options.Cluster, &utils.Process{}); loaded {
		errMsg := fmt.Sprintf("backup task is already running for cluster %s", options.Cluster)
		logger.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	defer lockMap.Delete(options.Cluster)

	// 3、关闭并行复制 & defer恢复
	workers, err := DisableParallelReplication(options)
	if err != nil {
		return nil, err
	}
	defer EnableParallelReplication(options, workers)

	// 4、启动xtrabackup备份
	err = backupExecCommand(options)
	if err != nil {
		return nil, err
	}

	// 5、备份完成后的收尾记录工作
	result, err := backupResult(options.logDir)
	if err != nil {
		logger.Warn("failed to parse backup result, error=(%v)", err)
		return nil, err
	}
	if options.bakFilePath != "" {
		result.FilePath = options.bakFilePath
	}

	return result, nil
}

// Stop 停止备份
func StopBackup(cluster string) error {
	if cluster == "" {
		return fmt.Errorf("cluster name cannot be empty")
	}

	// 从 lockMap 中获取进程对象
	value, ok := lockMap.Load(cluster)
	if !ok {
		return fmt.Errorf("no backup process found for cluster: %s", cluster)
	}

	process, ok := value.(*utils.Process)
	if !ok {
		return fmt.Errorf("invalid process object for cluster: %s", cluster)
	}

	// 停止进程
	err := process.Stop()
	if err != nil {
		logger.Error("failed to stop backup process for cluster %s: %v", cluster, err)
		return err
	}

	// 从 lockMap 中删除记录
	lockMap.Delete(cluster)

	logger.Info("backup process for cluster %s stopped successfully", cluster)
	return nil
}

// 检查备份进度
func CheckProcess() {}
