package bos

type Config struct {
	DbaBosBucket           string `yaml:"dba_bos_bucket"`
	DbaBosAccessKeyID      string `yaml:"dba_bos_accesskeyid"`
	DbaBosAccessKeySecret  string `yaml:"dba_bos_accesskeysecret"`
	DbaBosEndPoint         string `yaml:"dba_bos_endpoint"`
	SiodBosBucket          string `yaml:"siod_bos_bucket"`
	SiodBosAccessKeyID     string `yaml:"siod_bos_accesskeyid"`
	SiodBosAccessKeySecret string `yaml:"siod_bos_accesskeysecret"`
	SiodBosEndPoint        string `yaml:"siod_bos_endpoint"`
	NoahToken              string `yaml:"noah_token"`
	MysqlNormalPackage     string `yaml:"mysql-normal-package"` //安装包下载路径配置
	MysqlFdbPackage        string `yaml:"mysql-fdb-package"`
	BosRetryCount          int    `yaml:"bos-retry-count"`
}

var (
	cfg *Config
)

// 初始化BOS
func Init(conf *Config) error { return nil }
