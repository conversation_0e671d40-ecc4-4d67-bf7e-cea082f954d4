package bos

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func init() {
	cfg.DbaBosAccessKeyID = "77cbfd63fe57424db581eeac513ed86b"
	cfg.DbaBosAccessKeySecret = "6645ebbd54f94876ad2de7efeda9d957"
	cfg.DbaBosBucket = "dxm-dbbk-hb-test"
	cfg.DbaBosEndPoint = "hb-fsg.bcebos.com"
}

func TestCreatBosClient(t *testing.T) {
	Convey("test CreatDbaBosClient", t, func() {
		bosClient, err := CreatBosClient(cfg.DbaBosAccessKeyID, cfg.DbaBosAccessKeySecret, cfg.DbaBosEndPoint)
		So(err, ShouldBeNil)
		So(bosClient, ShouldNotBeNil)
	})
}

func TestUploadObjToBos(t *testing.T) {
	Convey("test UploadObjToBos", t, func() {
		sqls := "select * from test00.testa" // []string{}
		bosClient, err := UploadObjToBos(sqls, 20210924)
		So(err, ShouldBeNil)
		So(bosClient, ShouldNotBeNil)
	})
}

func TestBlockUploadToBos(t *testing.T) {
	Convey("test BlockUploadToBos", t, func() {
		filePath := "/Users/<USER>/binlogTestDir"
		bosPath := "20210924_Base/base"

		//分块上传数据
		isSuccess, err := BlockUploadToBos(filePath, bosPath)
		So(err, ShouldBeNil)
		So(isSuccess, ShouldBeTrue)
	})
}

func TestDownLoadSqlFileFromBos(t *testing.T) {
	Convey("test DownLoadDataFromBos", t, func() {
		baseDataDir := "20210924_Base/base"
		targetDir := "/Users/<USER>/base.sql"
		err := DownLoadDataFromBos(cfg.DbaBosAccessKeyID, cfg.DbaBosAccessKeySecret, cfg.DbaBosEndPoint, cfg.DbaBosBucket, baseDataDir, targetDir)
		So(err, ShouldBeNil)
	})
}

func TestDeleteBosData(t *testing.T) {
	Convey("test DownLoadDataFromBos", t, func() {
		dataDir := []string{"20210924_Base/base"}
		err := DeleteBosData(dataDir)
		So(err, ShouldBeNil)
	})
}
