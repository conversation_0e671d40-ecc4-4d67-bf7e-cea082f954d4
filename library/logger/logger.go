package logger

import (
	"go.uber.org/zap"
)

var (
	logger *zap.Logger
	atom   zap.AtomicLevel
)

type Logger interface {
	Debug(string, ...any)
	Info(string, ...any)
	Warn(string, ...any)
	Error(string, ...any)
}

func Debug(format string, v ...any) {
	logger.Sugar().Debugf(format, v...)
}

func Info(format string, v ...any) {
	logger.Sugar().Infof(format, v...)
}

func Warn(format string, v ...any) {
	logger.Sugar().Warnf(format, v...)
}

func Error(format string, v ...any) {
	logger.Sugar().Errorf(format, v...)
}

// 调整日志级别
func SetLevel(lvl string) {
	switch lvl {
	case "error":
		atom.SetLevel(zap.ErrorLevel)
	case "warn":
		atom.SetLevel(zap.WarnLevel)
	case "debug":
		atom.SetLevel(zap.DebugLevel)
	default:
		atom.SetLevel(zap.InfoLevel)
	}
}
