package logger

import (
	"os"
	"time"

	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

const (
	TIME_FORMAT = "2006-01-02 15:04:05.000"
)

type Config struct {
	Level      string `yaml:"level"`
	LogConsole string `yaml:"log_console"`
	Path       string `yaml:"path"`
	MaxSize    int    `yaml:"max_size"`
	MaxAge     int    `yaml:"max_age"`
	MaxBackups int    `yaml:"max_backups"`
}

// InitLogger
func Init(cfg *Config) *zap.Logger {
	// 创建Core三大件，进行初始化
	writeSyncer := getLogWriter(cfg.Path, cfg.MaxSize, cfg.MaxBackups, cfg.MaxAge)
	encoder := getEncoder()
	atom = zap.NewAtomicLevel()
	// 创建核心-->如果LogConsole为true，就在控制台和文件都打印，否则就只写到文件中
	var core zapcore.Core
	if cfg.LogConsole == "true" {
		//consoleEncoder := zapcore.NewConsoleEncoder(zap.NewDevelopmentEncoderConfig())
		// NewTee创建一个核心，将日志条目复制到两个或多个底层核心中。
		core = zapcore.NewTee(
			zapcore.NewCore(encoder, writeSyncer, atom),
			zapcore.NewCore(encoder, zapcore.Lock(os.Stdout), atom),
		)
	} else {
		core = zapcore.NewCore(encoder, writeSyncer, atom)
	}

	// 创建 logger 对象
	logger = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	SetLevel(cfg.Level)

	return logger

	// 替换全局的 logger, 后续在其他包中只需使用zap.L()调用即可
	//zap.ReplaceGlobals(Logger)
	// return &Logger{zaplogger}
}

// 获取切割的问题，给初始化logger使用的
func getLogWriter(filename string, maxSize, maxBackup, maxAge int) zapcore.WriteSyncer {
	// 使用 lumberjack 归档切片日志
	lumberJackLogger := &lumberjack.Logger{
		Filename:   filename,
		MaxSize:    maxSize,
		MaxBackups: maxBackup,
		MaxAge:     maxAge,
	}
	return zapcore.AddSync(lumberJackLogger)
}

// 获取Encoder，给初始化logger使用的
func getEncoder() zapcore.Encoder {
	return zapcore.NewConsoleEncoder(
		zapcore.EncoderConfig{
			TimeKey:          "ts",
			LevelKey:         "level",
			NameKey:          "logger",
			CallerKey:        "caller_line",
			FunctionKey:      zapcore.OmitKey,
			MessageKey:       "msg",
			StacktraceKey:    "stacktrace",
			LineEnding:       "\n",
			EncodeLevel:      cEncodeLevel,
			EncodeTime:       cEncodeTime,
			EncodeDuration:   zapcore.SecondsDurationEncoder,
			EncodeCaller:     cEncodeCaller,
			ConsoleSeparator: " ",
		})
}

// cEncodeLevel 自定义日志级别显示
func cEncodeLevel(level zapcore.Level, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString("[" + level.CapitalString() + "]")
}

// cEncodeTime 自定义时间格式显示
func cEncodeTime(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString("[" + time.Now().Format(TIME_FORMAT) + "]")
}

// cEncodeCaller 自定义行号显示
func cEncodeCaller(caller zapcore.EntryCaller, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendString("[" + caller.TrimmedPath() + "]")
}
