# XtraBackup 恢复功能实现

## 任务概述
基于现有的 backup.go 实现，为 library/xtrabackup/restore.go 实现完整的 MySQL 恢复逻辑。

## 设计原则
- 采用简化的函数式设计，与 StartBackup 函数风格保持一致
- 不使用 XtraBackupManager 对象设计
- 保持代码精简，避免不必要的复杂性

## 实现计划

### 1. 类型定义
- 完善 RestoreOptions 结构体
- 定义 RestoreResult 结构体
- 移除 XtraBackupManager 相关代码

### 2. 核心函数实现
- restoreArgsValidate: 参数校验和默认值设置
- download: 远程文件下载 (支持 mysql@ip:/path 格式)
- decompress: xbstream + qpress 解压缩
- prepare: 数据准备 (apply-log)
- Restore: 主恢复函数

### 3. 恢复流程
1. 下载全量和增量备份文件到临时目录
2. xbstream 解压到各自目录
3. qpress 解压缩
4. 数据准备 (apply-log)
5. 移动数据到目标目录

## 技术要点
- 支持增量备份的 LSN 校验
- 错误处理和临时文件清理
- 进度日志记录
- 与现有代码风格保持一致

## 实现完成

### 已完成功能
1. ✅ **类型定义**
   - 完善了 RestoreOptions 结构体，添加内部使用字段
   - 定义了 RestoreResult 结构体用于返回恢复结果
   - 移除了所有 XtraBackupManager 相关代码

2. ✅ **核心函数实现**
   - `restoreArgsValidate`: 参数校验和默认值设置
   - `download`: 远程文件下载，支持 mysql@ip:/path 格式
   - `decompress`: xbstream + qpress 解压缩
   - `prepare`: 数据准备，包含 apply-log 逻辑
   - `Restore`: 主恢复函数，整合所有步骤

3. ✅ **辅助函数**
   - `applyLogToBackup`: 对备份应用日志
   - `applyIncrementalBackup`: 应用增量备份
   - `moveDataToTarget`: 移动数据到目标目录

4. ✅ **配置更新**
   - 更新了 init.go，添加 tmpPath 初始化
   - 保持与现有代码风格一致

5. ✅ **测试验证**
   - 创建了基本的单元测试
   - 验证了参数校验功能
   - 测试通过

6. ✅ **使用示例**
   - 创建了完整的使用示例
   - 包含全量备份和增量备份恢复场景

### 主要特性
- **简洁设计**: 采用函数式设计，与 StartBackup 风格一致
- **完整流程**: 支持下载、解压、准备、恢复的完整流程
- **增量支持**: 支持多个增量备份的依次应用
- **错误处理**: 完善的错误处理和临时文件清理
- **进度日志**: 详细的操作日志记录
- **灵活配置**: 支持自定义内存使用和并行线程数

### 使用方法
```go
// 初始化配置
config := &xtrabackup.Config{
    BinPath:   "/usr/bin",
    TmpPath:   "/tmp/xtrabackup",
    LogPath:   "/var/log/xtrabackup",
    UseMemory: "1G",
}
xtrabackup.Init(config)

// 配置恢复选项
options := &xtrabackup.RestoreOptions{
    FullBackupFile: "mysql@host:/path/full.xbstream",
    IncrementalFiles: []string{"mysql@host:/path/incr1.xbstream"},
    TargetDataDir: "/var/lib/mysql/data",
}

// 执行恢复
result, err := xtrabackup.Restore(options)
```
