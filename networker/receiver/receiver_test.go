package receiver

import (
	"context"
	"sync"
	"testing"
	"time"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/smartystreets/goconvey/convey"
	"google.golang.org/grpc"

	"mdc-agent/common"
)

//创建rpc Server
func TestNewGrpcServer(t *testing.T) {
	Convey("NewGrpcServer：创建rpc Server", t, func() {
		var serverType *grpc.Server
		So(InitRpcServer(), ShouldHaveSameTypeAs, serverType)
	})
}

//启动&关闭 Server
func TestStartAgentServer(t *testing.T) {
	Convey("启动&关闭 rpc Server", t, func() {
		//主协程等待变量
		waitgroup := new(sync.WaitGroup)
		waitgroup.Add(1)
		grpcServer := InitRpcServer()
		go StartAgentServer(waitgroup, grpcServer)
		StopAgentServer(waitgroup, grpcServer)

		So(grpcServer.GetServiceInfo(), ShouldBeEmpty)
	})
}

//将消息放到入tinker消息队列中
func TestPutMsgToChan(t *testing.T) {
	//变量声明
	frontMsg := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_START_BINLOG,
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterName: "test",
			ClusterId:   1,
			NodeId:      1,
		},
		MsgType: &pb_agent.MdcAgentAsyncMsg_StartXtrabkExecute{

		},
	}

	resposMsg := pb_server.MdcServerTaskResMsg{}
	Convey("PutMsgToChan：将消息放到入tinker消息队列中", t, func() {
		err := PutMsMsgToChan(&frontMsg)
		So(err, ShouldBeNil)
	})
	//写满队列
	for i := 0; len(common.AgentMsgChan) < (cap(common.AgentMsgChan) - 1); i++ {
		common.AgentMsgChan <- &frontMsg
	}

	contx, _ := context.WithTimeout(context.Background(), time.Second*1)
	conn, _ := grpc.DialContext(contx, "0.0.0.0:10000", grpc.WithBlock(), grpc.WithInsecure())
	client := pb_agent.NewMdcAgentClient(conn)
	Convey("PutMsgToChan：队列写满报错", t, func() {
		err := PutMsMsgToChan(&frontMsg)
		So(err, ShouldBeNil)
		respons, err := client.ReceiveAsyncTaskMsgFromServer(contx, &frontMsg)
		So(respons, ShouldHaveSameTypeAs, &resposMsg)
		So(err, ShouldNotBeNil)
	})
}
