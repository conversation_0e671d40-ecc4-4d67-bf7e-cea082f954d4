package worker

import (
	"fmt"
	"mdc-agent/common"
	"testing"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/smartystreets/goconvey/convey"
)

func TestHandleCheckBkStatus(t *testing.T) {
	taskMsg := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_CHECK_STATUS,
		MsgType: &pb_agent.MdcAgentSyncMsg_CheckBkmysqlStatus{
			CheckBkmysqlStatus: &pb_agent.CheckBkMysqlStatus{
				BkIp:   "*************",
				BkPath: "/home/<USER>/genggangfeng/mysql",
			},
		},
	}
	res, err := HandleCheckBkStatus(&taskMsg)
	Convey("test HandleCheckBkStatus", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
		fmt.Println(res)
	})
}

func TestHandleGetFreeCap(t *testing.T) {
	taskMsg := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_GET_FREE_CAP,
		MsgType: &pb_agent.MdcAgentSyncMsg_GetFreeStorage{
			GetFreeStorage: &pb_agent.GetFreeStorage{
				DataDir: "/home/<USER>/genggangfeng/mysql",
			},
		},
	}
	res, err := HandleGetFreeCap(&taskMsg)
	Convey("test TestHandleGetFreeCap", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
		fmt.Println(res)
	})
}

func TestHandleCleanData(t *testing.T) {
	taskMsg := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_CLEAN_DATA,
		MsgType: &pb_agent.MdcAgentSyncMsg_CleanData{
			CleanData: &pb_agent.CleanData{
				CleanDataType: pb_server.DataType_BK_REMOTE_DATA,
				DataDir:       []string{},
			},
		},
	}
	err := HandleCleanData(&taskMsg)
	Convey("test HandleCheckBkStatus", t, func() {
		So(err, ShouldBeNil)
	})

	taskMsg = pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_CLEAN_DATA,
		MsgType: &pb_agent.MdcAgentSyncMsg_CleanData{
			CleanData: &pb_agent.CleanData{
				CleanDataType: pb_server.DataType_RS_REMOTE_DATA,
				DataDir:       []string{},
			},
		},
	}
	err = HandleCleanData(&taskMsg)
	Convey("test HandleCheckBkStatus", t, func() {
		So(err, ShouldBeNil)
	})

	taskMsg = pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_CLEAN_DATA,
		MsgType: &pb_agent.MdcAgentSyncMsg_CleanData{
			CleanData: &pb_agent.CleanData{
				CleanDataType: pb_server.DataType_BOS_DATA,
				DataDir:       []string{},
			},
		},
	}
	err = HandleCleanData(&taskMsg)
	Convey("test HandleCleanData", t, func() {
		So(err, ShouldBeNil)
	})
}

func TestHandleDeleteBinlogBk(t *testing.T) {
	taskMsg := pb_agent.MdcAgentSyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_DELETE_BINLOGBK,
		MsgType: &pb_agent.MdcAgentSyncMsg_DeleteBinlogBk{
			DeleteBinlogBk: &pb_agent.DeleteBinlogBk{
				BkBinlogDir:          "/Users/<USER>/binlogTestDir",
				BinlogFileNameResult: "mysql-bin.000001,mysql-bin.000002",
			},
		},
	}
	err := HandleDeleteBinlogBk(&taskMsg)
	Convey("test HandleDeleteBinlogBk", t, func() {
		So(err, ShouldBeNil)
	})
}

func TestHandleUnpackData(t *testing.T) {
	taskMsg := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_UNPACK_DATA,
		MsgType: &pb_agent.MdcAgentAsyncMsg_UnpackData{
			UnpackData: &pb_agent.UnpackData{
				DataSource: []*pb_agent.PackDataBase{},
				TargetPath: "/Users/<USER>/Go/src/testunpack/base",
			},
		},
	}
	_, err := HandleUnpackData(&taskMsg)
	Convey("test HandleUnpackData", t, func() {
		So(err, ShouldBeNil)
	})
}

func TestGetUploadRemoteBinlogFileName(t *testing.T) {
	Convey("test HandleUnpackData", t, func() {
		beginUpdateTime, newEndUpadetime, binlogFileNameResult, binlogFullFileNameResult, err := GetUploadBinlogFileList("/Users/<USER>/binlogTestDir", "")
		So(err, ShouldBeNil)
		fmt.Println(beginUpdateTime, newEndUpadetime, binlogFileNameResult, binlogFullFileNameResult)
	})
}

func TestGetBinlogFileName(t *testing.T) {
	Convey("test GetBinlogFileName", t, func() {
		fileNameList, err := GetBinlogFileName("/Users/<USER>/binlogTestDir", common.BinlogPathName)
		So(fileNameList, ShouldNotBeNil)
		So(err, ShouldBeNil)
		fmt.Println(fileNameList)
	})
}
