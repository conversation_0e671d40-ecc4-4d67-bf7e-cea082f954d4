package worker

import (
	"fmt"
	"testing"
	"time"

	"github.com/go-mysql-org/go-mysql/replication"
	. "github.com/smartystreets/goconvey/convey"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	"mdc-agent/common"
)

func TestHandleDownloadRestoreData(t *testing.T) {
	taskMsg := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_DOWNLOAD_RS_DATA,
		MsgType: &pb_agent.MdcAgentAsyncMsg_DownloadRestoreData{
			DownloadRestoreData: &pb_agent.DownloadRestoreData{
				SourceType:   pb_agent.SourceType_REMOTE,
				BaseDataPath: "",
				IncDataPath:  []string{},
				BinlogPath:   []string{},
				TargetPath:   "",
			},
		},
	}
	res, err := HandleDownloadRestoreData(&taskMsg)
	Convey("test HandleDownloadRestoreData", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
	})

	taskMsg = pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_DOWNLOAD_RS_DATA,
		MsgType: &pb_agent.MdcAgentAsyncMsg_DownloadRestoreData{
			DownloadRestoreData: &pb_agent.DownloadRestoreData{
				SourceType:   pb_agent.SourceType_DBA_BOS,
				BaseDataPath: "",
				IncDataPath:  []string{},
				BinlogPath:   []string{},
				TargetPath:   "",
			},
		},
	}
	res, err = HandleDownloadRestoreData(&taskMsg)
	Convey("test HandleDownloadRestoreData", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
	})
}

func TestHandleDownloadBinlogData(t *testing.T) {
	taskMsg := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_DOWNLOAD_BINLOG_DATA,
		MsgType: &pb_agent.MdcAgentAsyncMsg_DownloadBinlogData{
			DownloadBinlogData: &pb_agent.DownloadBinlogData{
				SourceType:     pb_agent.SourceType_REMOTE,
				DataPath:       []string{},
				RemoteDataPath: []string{},
				TargetPath:     "",
			},
		},
	}
	res, err := HandleDownloadBinlogData(&taskMsg)
	Convey("test HandleDownloadRestoreData", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
	})
}

func TestHandleExecRestore(t *testing.T) {
	taskMsg := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_RESTORE,
		MsgType: &pb_agent.MdcAgentAsyncMsg_ExecRestore{
			ExecRestore: &pb_agent.ExecRestore{
				RestoreType:  0,
				BaseDataPath: "",
				IncDataPath:  []string{},
				BinlogPath:   []string{},
				RsTime:       "",
				RsTableName:  "",
				AccountUser:  "",
			},
		},
	}
	_, err := HandleExecRestore(&taskMsg)
	Convey("test HandleExecRestore", t, func() {
		So(err, ShouldBeNil)
	})
}

func TestGenerateTmplFile(t *testing.T) {
	Convey("test generateTmplFile", t, func() {
		err := generateTmplFile("../../conf/template", "../../conf/mysqlconf", "/Users/<USER>/geneTest", "/Users/<USER>/geneTest", 6667)
		So(err, ShouldBeNil)
	})
}

//func TestGenerateInstallFile(t *testing.T) {
//	Convey("test generateInstallFile", t, func() {
//		common.Config.FtpDownloadPath = "ftp://dba-ftp.duxiaoman-int.com:80"
//		common.Config.MysqlNormalPackage = "install_cluster_soft/matrix/mysql/general/mysql.tar.gz"
//		_, err := generateInstallFile("../../conf/template", "../../conf/mysqlconf", "/Users/<USER>/geneTest", 6667)
//		So(err, ShouldBeNil)
//	})
//}
//
//func TestInstallMysql(t *testing.T) {
//	Convey("test generateInstallFile", t, func() {
//		common.Config.FtpDownloadPath = "ftp://dba-ftp.duxiaoman-int.com:80"
//		common.Config.MysqlNormalPackage = "install_cluster_soft/matrix/mysql/general/mysql.tar.gz"
//		_, _, err := installMysql("/Users/<USER>/geneTest", "/Users/<USER>/geneTest")
//		So(err, ShouldBeNil)
//	})
//}

func TestHandleExecParseBinlog(t *testing.T) {
	taskMsg := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_EXEC_PARSE_BINLOG,
		MsgType: &pb_agent.MdcAgentAsyncMsg_ExecParseBinlog{
			ExecParseBinlog: &pb_agent.ExecParseBinlog{
				StartTime:  "",
				Endtime:    "",
				BkIp:       "",
				BkPort:     0,
				BkPath:     "",
				BinlogPath: []string{},
			},
		},
	}
	res, err := HandleExecParseBinlog(&taskMsg)
	Convey("test HandleExecRestore", t, func() {
		So(err, ShouldBeNil)
		So(res, ShouldNotBeNil)
	})
}

func TestGetEventHead(t *testing.T) {
	Convey("test parse binlog", t, func() {
		var previousEvent *replication.PreviousGTIDsEvent
		var eventTime uint32

		binlogFile := "/Users/<USER>/Downloads/mysql-bin.010413"
		binlogPraser := replication.NewBinlogParser()
		binlogPraser.ParseFile(binlogFile, 4, func(event *replication.BinlogEvent) error {
			if event.Header.EventType == replication.PREVIOUS_GTIDS_EVENT {
				previousEvent = event.Event.(*replication.PreviousGTIDsEvent)
				eventTime = event.Header.Timestamp
			}
			return nil
		})

		fmt.Println(previousEvent)
		tm := time.Unix(int64(eventTime), 0)
		fmt.Println(eventTime)
		fmt.Println(tm.Format(common.TIME_FORMAT))
	})
}

func Test_makeMysqlPort(t *testing.T) {
	got := makeMysqlPort()
	fmt.Println(got)
}
