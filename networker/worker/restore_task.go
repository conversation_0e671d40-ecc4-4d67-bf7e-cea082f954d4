package worker

import (
	"dt-common/global"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math/rand"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"

	"github.com/go-mysql-org/go-mysql/replication"

	"mdc-agent/common"
	"mdc-agent/library/binlog2sql"
	"mdc-agent/library/bos"
)

type UploadBosMeta struct {
	UploadBosPath      string `json:"uploadBosPath"`
	UploadBosFileCount int64  `json:"uploadBosFileCount"`
}

//处理下载恢复数据任务
func HandleDownloadRestoreData(taskMsg *pb_agent.MdcAgentAsyncMsg) (res *pb_server.DownLoadRsDataReport, err error) {
	baseDataDir := taskMsg.GetDownloadRestoreData().GetBaseDataPath()
	incDataDir := taskMsg.GetDownloadRestoreData().GetIncDataPath()
	binlogDatadir := taskMsg.GetDownloadRestoreData().GetBinlogPath()
	sourceType := taskMsg.GetDownloadRestoreData().SourceType
	targetPath := taskMsg.GetDownloadRestoreData().TargetPath
	if baseDataDir == "" && incDataDir == nil && len(incDataDir) == 0 && binlogDatadir == nil && len(binlogDatadir) == 0 {
		errMsg := fmt.Sprintf("source dataDir is empty,task_id=[%v],cluster_id=[%v]", taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	var (
		targetBaseDataPath   string
		targetIncDataPath    = make([]string, 0)
		targetBinlogDataPath = make([]string, 0)
		tempBinlogDataPath   = make([]string, 0)
	)

	//创建恢复目录
	if err = os.MkdirAll(targetPath, 0777); err != nil {
		errMsg := fmt.Sprintf("HandleDownloadRestoreData MkdirAll failed,err=[%v],targetPath=[%v]", err, targetPath)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	//下载全备数据文件
	if baseDataDir != "" {
		if err, targetBaseDataPath = DownloadSourceData(sourceType, baseDataDir, targetPath, false); err != nil {
			errMsg := fmt.Sprintf("DownloadSourceData failed,err=[%v],dataDir=[%v],task_id=[%v],cluster_id=[%v]", err, baseDataDir, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
			common.Log.Warn(errMsg)
			return nil, errors.New(errMsg)
		}
	}
	//下载增备数据
	if incDataDir != nil && len(incDataDir) != 0 {
		for _, val := range incDataDir {
			if val == "" {
				continue
			}
			err, incDir := DownloadSourceData(sourceType, val, targetPath, false)
			if err != nil {
				errMsg := fmt.Sprintf("DownloadSourceData failed,err=[%v],dataDir=[%v],task_id=[%v],cluster_id=[%v]", err, val, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
				common.Log.Warn(errMsg)
				return nil, errors.New(errMsg)
			}
			targetIncDataPath = append(targetIncDataPath, incDir)
		}
	}
	//下载binlog数据
	if binlogDatadir != nil && len(binlogDatadir) != 0 {
		for _, val := range binlogDatadir {
			if val == "" {
				continue
			}
			err, binlogDir := DownloadSourceData(taskMsg.GetDownloadRestoreData().BinlogSourceType, val, targetPath, true)
			if err != nil {
				errMsg := fmt.Sprintf("DownloadSourceData failed,err=[%v],dataDir=[%v],task_id=[%v],cluster_id=[%v]", err, val, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
				common.Log.Warn(errMsg)
				return nil, errors.New(errMsg)
			}
			tempBinlogDataPath = append(tempBinlogDataPath, binlogDir)
		}
		if len(tempBinlogDataPath) == 0 {
			errMsg := fmt.Sprintf("DownLoadSqlFileFromBos failed,targetBinlog is nil")
			common.Log.Warn(errMsg)
			return nil, errors.New(errMsg)
		}
		if taskMsg.GetDownloadRestoreData().BinlogSourceType == pb_agent.SourceType_REMOTE {
			targetBinlogDataPath = tempBinlogDataPath
		} else {
			for _, value := range tempBinlogDataPath {
				if value == "" {
					continue
				}
				//执行解压操作
				tarShell := fmt.Sprintf("cd %v && tar -xvf %v", targetPath, value)
				_, _, err = common.SyncExecShell(tarShell)
				if err != nil {
					errMsg := fmt.Sprintf("SyncExecShell tar failed,err=[%v],targetPath=[%v],shell=[%v]", err, targetPath, tarShell)
					common.Log.Error(errMsg)
					return nil, errors.New(errMsg)
				}
			}
			//获得binlog文件目录
			targetBinlogDataPath, err = GetBinlogFileName(targetPath, common.BinlogPathName)
			if err != nil || len(targetBinlogDataPath) == 0 {
				errMsg := fmt.Sprintf("GetBinlogFileName failed,err=[%v],targetPath=[%v]", err, targetPath)
				common.Log.Error(errMsg)
				return nil, errors.New(errMsg)
			}
		}
	}

	//构建汇报任务
	res = &pb_server.DownLoadRsDataReport{
		BaseDataPath: targetBaseDataPath,
		IncDataPath:  targetIncDataPath,
		BinlogPath:   targetBinlogDataPath,
		TargetPath:   targetPath,
		MdcAgentPort: taskMsg.GetDownloadRestoreData().GetMdcAgentPort(),
	}
	return res, nil
}

//判断数据类型,获取数据
func DownloadSourceData(sourceType pb_agent.SourceType, baseDataDir, targetPath string, isRemote bool) (error, string) {
	var fileName, targetFileName string
	//判断数据类型
	switch sourceType {
	case pb_agent.SourceType_REMOTE:
		if isRemote {
			//解析目录 获得数据目录和远端机器IP
			bkBostFileStr := strings.Split(baseDataDir, ":")
			if len(bkBostFileStr) >= 2 {
				fileName = bkBostFileStr[len(bkBostFileStr)-1]
			}
			if fileName != "" {
				//从大磁盘机器上拷贝数据
				if err := common.ScpData(bkBostFileStr[0], bkBostFileStr[1], targetPath); err != nil {
					errMsg := fmt.Sprintf("ScpData failed,err=[%v],dataDir=[%v]", err, baseDataDir)
					common.Log.Warn(errMsg)
					return errors.New(errMsg), ""
				}
			}
			targetFileName = fmt.Sprintf("%v/%v", targetPath,
				strings.Split(bkBostFileStr[1], "/")[len(strings.Split(bkBostFileStr[1], "/"))-1])
		} else {
			if bkBostFileStr := strings.Split(baseDataDir, "/"); len(bkBostFileStr) >= 2 {
				fileName = bkBostFileStr[len(bkBostFileStr)-1]
			}
			if fileName != "" {
				targetFileName = fmt.Sprintf("%v/%v", targetPath, fileName)
				err := bos.DownLoadDataFromBos(common.Config.DbaBosAccessKeyID, common.Config.DbaBosAccessKeySecret, common.Config.DbaBosEndPoint, common.Config.DbaBosBucket, baseDataDir, targetFileName)
				if err != nil {
					errMsg := fmt.Sprintf("DownLoadDataFromBos failed,err=[%v],dataDir=[%v],targetFileName=[%v]", err, baseDataDir, targetFileName)
					common.Log.Warn(errMsg)
					return errors.New(errMsg), ""
				}
			}
		}
	case pb_agent.SourceType_DBA_BOS:
		if bkBostFileStr := strings.Split(baseDataDir, "/"); len(bkBostFileStr) >= 2 {
			fileName = bkBostFileStr[len(bkBostFileStr)-1]
		}
		if fileName != "" {
			targetFileName = fmt.Sprintf("%v/%v", targetPath, fileName)
			err := bos.DownLoadDataFromBos(common.Config.DbaBosAccessKeyID, common.Config.DbaBosAccessKeySecret, common.Config.DbaBosEndPoint, common.Config.DbaBosBucket, baseDataDir, targetFileName)
			if err != nil {
				errMsg := fmt.Sprintf("DownLoadDataFromBos failed,err=[%v],dataDir=[%v],targetFileName=[%v]", err, baseDataDir, targetFileName)
				common.Log.Warn(errMsg)
				return errors.New(errMsg), ""
			}
		}
	case pb_agent.SourceType_SIOS_BOS:
		if bkBostFileStr := strings.Split(baseDataDir, ":"); len(bkBostFileStr) >= 2 {
			baseDataDir = bkBostFileStr[len(bkBostFileStr)-1]
		}
		if bkBostFileStr := strings.Split(baseDataDir, "/"); len(bkBostFileStr) >= 2 {
			fileName = bkBostFileStr[len(bkBostFileStr)-1]
		}
		if fileName != "" {
			targetFileName = fmt.Sprintf("%v/%v", targetPath, fileName)
			err := bos.DownLoadDataFromBos(common.Config.SiodBosAccessKeyID, common.Config.SiodBosAccessKeySecret, common.Config.SiodBosEndPoint, common.Config.SiodBosBucket, baseDataDir, targetFileName)
			if err != nil {
				errMsg := fmt.Sprintf("DownLoadDataFromBos failed,err=[%v],dataDir=[%v],targetFileName=[%v]", err, baseDataDir, targetFileName)
				common.Log.Warn(errMsg)
				return errors.New(errMsg), ""
			}
		}
	case pb_agent.SourceType_TAPE_LIB:
		if bkBostFileStr := strings.Split(baseDataDir, "/"); len(bkBostFileStr) >= 2 {
			fileName = bkBostFileStr[len(bkBostFileStr)-1]
		}
		if fileName != "" {
			targetFileName = fmt.Sprintf("%v/%v", targetPath, fileName)
			err := bos.DownLoadDataFromTape(baseDataDir, targetFileName)
			if err != nil {
				errMsg := fmt.Sprintf("DownLoadDataFromTape failed,err=[%v],dataDir=[%v],targetFileName=[%v]", err, baseDataDir, targetFileName)
				common.Log.Warn(errMsg)
				return errors.New(errMsg), ""
			}
		}
	default:
		errMsg := fmt.Sprintf("unknown source type,type=[%v]", sourceType)
		common.Log.Notice(errMsg)
		return errors.New(errMsg), ""
	}
	return nil, targetFileName
}

// 下载恢复合集
func HandleDownloadBinlogData(taskMsg *pb_agent.MdcAgentAsyncMsg) (res *pb_server.DownLoadBinlogDataReport, err error) {
	dataDir := taskMsg.GetDownloadBinlogData().DataPath
	remoteDataDir := taskMsg.GetDownloadBinlogData().RemoteDataPath
	sourceType := taskMsg.GetDownloadBinlogData().SourceType
	targetPath := taskMsg.GetDownloadBinlogData().TargetPath
	if dataDir == nil && len(dataDir) == 0 && remoteDataDir == nil && len(remoteDataDir) == 0 {
		errMsg := fmt.Sprintf("source dataDir is empty,task_id=[%v],cluster_id=[%v]", taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}

	//创建恢复目录
	if err = os.MkdirAll(targetPath, os.ModePerm); err != nil {
		errMsg := fmt.Sprintf("HandleDownloadBinlogData MkdirAll failed,err=[%v],targetPath=[%v]", err, targetPath)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	targetBinlogDataPath := make([]string, 0)
	//下载xtra备份数据
	if len(dataDir) != 0 {
		for _, val := range dataDir {
			err, incDir := DownloadSourceData(sourceType, val, targetPath, false)
			if err != nil {
				errMsg := fmt.Sprintf("DownLoadSqlFileFromBos failed,err=[%v],dataDir=[%v],task_id=[%v],cluster_id=[%v]", err, val, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
				common.Log.Warn(errMsg)
				return nil, errors.New(errMsg)
			}
			targetBinlogDataPath = append(targetBinlogDataPath, incDir)
		}
	}
	//下载binlog数据
	if len(remoteDataDir) != 0 {
		for _, val := range remoteDataDir {
			err, binlogDir := DownloadSourceData(sourceType, val, targetPath, true)
			if err != nil {
				errMsg := fmt.Sprintf("DownLoadSqlFileFromBos failed,err=[%v],dataDir=[%v],task_id=[%v],cluster_id=[%v]", err, val, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
				common.Log.Warn(errMsg)
				return nil, errors.New(errMsg)
			}
			targetBinlogDataPath = append(targetBinlogDataPath, binlogDir)
		}
	}

	//执行解压操作
	if len(targetBinlogDataPath) == 0 {
		errMsg := fmt.Sprintf("DownLoadSqlFileFromBos failed,targetBinlog is nil")
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	/*
		for _, value := range targetBinlogDataPath {
			//解压
			tarShell := fmt.Sprintf("cd %v && tar -xvf %v", targetPath, value)
			_, _, err = common.SyncExecShell(tarShell)
			if err != nil {
				errMsg := fmt.Sprintf("SyncExecShell tar failed,err=[%v],targetPath=[%v],shell=[%v]", err, targetPath, tarShell)
				common.Log.Error(errMsg)
				return nil, errors.New(errMsg)
			}
		}*/
	/*
		//获得binlog文件目录
		binlogPathDir, err := GetBinlogFileName(targetPath, common.BinlogPathName)
		if err != nil || len(binlogPathDir) == 0 {
			errMsg := fmt.Sprintf("GetBinlogFileName failed,err=[%v],targetPath=[%v]", err, targetPath)
			common.Log.Error(errMsg)
			return nil, errors.New(errMsg)
		}*/

	//构建汇报任务
	res = &pb_server.DownLoadBinlogDataReport{
		BinlogPath:   targetBinlogDataPath,
		MdcAgentPort: taskMsg.GetDownloadBinlogData().GetMdcAgentPort(),
	}
	return res, nil

}

//处理恢复请求
func HandleExecRestore(taskMsg *pb_agent.MdcAgentAsyncMsg) (*pb_server.ExecRestoreReport, error) {
	rsType := taskMsg.GetExecRestore().RestoreType
	baseDataPath := taskMsg.GetExecRestore().GetBaseDataPath()
	incDataPath := taskMsg.GetExecRestore().GetIncDataPath()
	binlogDataPath := taskMsg.GetExecRestore().GetBinlogPath()
	restorePartitionConfig := taskMsg.GetExecRestore().GetRestorePartitionConfig()
	//rsTime := taskMsg.GetExecRestore().RsTime
	//incDataEndTime := taskMsg.GetExecRestore().IncDataEndTime
	isFdb := taskMsg.GetExecRestore().IsFdb
	targetPath := taskMsg.GetExecRestore().TargetPath
	applicantRole := taskMsg.GetExecRestore().ApplicantRole
	if baseDataPath == "" && incDataPath == nil && len(incDataPath) == 0 && binlogDataPath == nil && len(binlogDataPath) == 0 {
		errMsg := fmt.Sprintf("source dataDir is empty,task_id=[%v],cluster_id=[%v]", taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	// 根据任务类型不同进入不同分支
	switch global.RsType(rsType) {
	case global.AnyTime, global.Snapshot, global.RestoreInstance:
		// 恢复天级别快照
		err, rsDataTargetPath := rsSnapshotData(targetPath, baseDataPath, incDataPath)
		if err != nil {
			errMsg := fmt.Sprintf("rsSnapshotData failed,err=[%v],task_id=[%v],cluster_id=[%v]", err, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
			common.Log.Warn(errMsg)
			return nil, errors.New(errMsg)
		}
		// TODO: 旧逻辑是根据角色判断是否启动实例，后期需要明确指定类型是否要指定实例。
		// 如果恢复类型为恢复实例。用户为非dba时（旧逻辑）。 则需要启动实例。
		if applicantRole != int32(common.Dba) || global.RestoreInstance == global.RsType(rsType) {
			passWord, _, port, err := startInstance(isFdb, targetPath, rsDataTargetPath, taskMsg)
			if err != nil {
				errMsg := fmt.Sprintf("startInstance failed,err=[%v],task_id=[%v],cluster_id=[%v]", err, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
				common.Log.Warn(errMsg)
				return nil, errors.New(errMsg)
			}

			res := &pb_server.ExecRestoreReport{
				DbUser:       taskMsg.GetExecRestore().AccountUser,
				DbPassWord:   passWord,
				InstanceIp:   taskMsg.GetExecRestore().InstanceIp,
				InstancePort: strconv.Itoa(port),
			}
			return res, nil
		} else {
			common.Log.Warn("HandleExecRestore finished,applicantRole=[%v],targetPath=[%v]", applicantRole, rsDataTargetPath)
			return nil, nil
		}
		// 恢复天级别快照并分类打包将其上传到bos中
	case global.RestorePartition:
		common.Log.Info("[HandleExecRestore] restore type is RestorePartition. start restore...")
		// 恢复数据到指定机器目录
		err, rsDataTargetPath := rsSnapshotData(targetPath, baseDataPath, incDataPath)
		if err != nil {
			errMsg := fmt.Sprintf("rsSnapshotData failed,err=[%v],task_id=[%v],cluster_id=[%v]", err, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
			common.Log.Warn(errMsg)
			return nil, errors.New(errMsg)
		}
		common.Log.Info("rsSnapshotData RestorePartition success. rsDataTargetPath=[%v]", rsDataTargetPath)
		// 将文件根据映射移动至指定目录
		// 上传至bos
		err, partitionConfigStr := restorePartition(restorePartitionConfig, rsDataTargetPath, strconv.FormatInt(taskMsg.TaskId, 10))
		if err != nil {
			return nil, err
		}
		res := &pb_server.ExecRestoreReport{
			PartitionBosMapping: partitionConfigStr,
		}
		common.Log.Info("[HandleExecRestore] restore type is RestorePartition. restore succesfully...")
		return res, nil
	default:
		return nil, fmt.Errorf("not support this restore type. rsType=[%v]", rsType)

	}

	////判断binlog数据是否存在
	//if binlogDataPath == nil || len(binlogDataPath) == 0 || rsType == int32(common.Snapshot) {
	//	common.Log.Info("HandleExecRestore,binlogDataPath=[%v],rsType=[%v]", binlogDataPath, rsType)
	//	return res, nil
	//}
	////恢复数据到具体时间点
	//if err := rsSpecificTimeData(instanceDir, incDataEndTime, rsTime, binlogDataPath, taskMsg); err != nil {
	//	errMsg := fmt.Sprintf("rsSnapshotData failed,err=[%v],task_id=[%v],cluster_id=[%v]", err, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
	//	common.Log.Warn(errMsg)
	//	return nil, errors.New(errMsg)
	//}
}

//应用数据到天级快照
func rsSnapshotData(targetPath, baseDataPath string, incDataPath []string) (error, string) {
	//应用全备数据
	var dataTargetPath string
	if baseDataPath != "" {
		baseDataPathList := strings.Split(baseDataPath, "/")
		if len(baseDataPathList) < 2 {
			errMsg := fmt.Sprintf("rsSnapshotData failed,baseDataPath=[%v],baseDataPathList=[%v]", baseDataPath, baseDataPathList)
			common.Log.Warn(errMsg)
			return errors.New(errMsg), ""
		}
		fileTokenList := strings.Split(baseDataPathList[len(baseDataPathList)-1], ".")
		if len(fileTokenList) < 2 {
			errMsg := fmt.Sprintf("rsSnapshotData failed,baseDataPath=[%v],baseDataPathList=[%v]", baseDataPath, baseDataPathList)
			common.Log.Warn(errMsg)
			return errors.New(errMsg), ""
		}
		dataTargetPath = fmt.Sprintf("%v/%v/%v", targetPath, common.DataDir, fileTokenList[0])
		// 判断恢复数据集合是否只有一个
		if incDataPath == nil || len(incDataPath) == 0 {
			if err := rsXtraDataFile(dataTargetPath, baseDataPath, "", fileTokenList[0], true, true); err != nil {
				errMsg := fmt.Sprintf("rsXtraDataFile failed,err=[%v],dataDir=[%v]", err, baseDataPath)
				common.Log.Warn(errMsg)
				return errors.New(errMsg), ""
			}
		} else {
			if err := rsXtraDataFile(dataTargetPath, baseDataPath, "", fileTokenList[0], true, false); err != nil {
				errMsg := fmt.Sprintf("rsXtraDataFile failed,err=[%v],dataDir=[%v]", err, baseDataPath)
				common.Log.Warn(errMsg)
				return errors.New(errMsg), ""
			}
		}
	}

	//应用增备数据
	if incDataPath != nil && len(incDataPath) != 0 {
		for idx, val := range incDataPath {
			if val == "" {
				continue
			}
			incDataPathList := strings.Split(val, "/")
			if len(incDataPathList) < 2 {
				errMsg := fmt.Sprintf("rsSnapshotData failed,incDataPath=[%v],incDataPathList=[%v]", val, incDataPathList)
				common.Log.Warn(errMsg)
				return errors.New(errMsg), ""
			}
			fileTokenList := strings.Split(incDataPathList[len(incDataPathList)-1], ".")
			if len(fileTokenList) < 2 {
				errMsg := fmt.Sprintf("rsSnapshotData failed,incDataPath=[%v],incDataPathList=[%v]", val, incDataPathList)
				common.Log.Warn(errMsg)
				return errors.New(errMsg), ""
			}
			incdataTargetPath := fmt.Sprintf("%v/%v/%v", targetPath, common.DataDir, fileTokenList[0])
			if idx == len(incDataPath)-1 {
				if err := rsXtraDataFile(incdataTargetPath, val, dataTargetPath, fileTokenList[0], false, true); err != nil {
					errMsg := fmt.Sprintf("rsXtraDataFile failed,err=[%v],dataDir=[%v]", err, val)
					common.Log.Warn(errMsg)
					return errors.New(errMsg), ""
				}
			} else {
				if err := rsXtraDataFile(incdataTargetPath, val, dataTargetPath, fileTokenList[0], false, false); err != nil {
					errMsg := fmt.Sprintf("rsXtraDataFile failed,err=[%v],dataDir=[%v]", err, val)
					common.Log.Warn(errMsg)
					return errors.New(errMsg), ""
				}
			}
		}
	}
	//执行move back操作
	if err := moveBackData(dataTargetPath); err != nil {
		errMsg := fmt.Sprintf("moveBackData failed,err=[%v],dataDir=[%v]", err, dataTargetPath)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), ""
	}

	return nil, dataTargetPath
}

//应用xtra备份的数据
func rsXtraDataFile(targetPath, rsDataPath, baseDir, fileName string, isFull, end bool) error {
	//执行备份数据包解压操作
	curPath, err := os.Getwd()
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell get curPath failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	isXbstreamType := strings.Contains(rsDataPath, common.XbstreamType)
	xbStreamBin := fmt.Sprintf("%v/%v", curPath, common.XbstreamBinPath)
	unxbStreamDataDir := fmt.Sprintf("%v/%v", targetPath, fileName)

	if isXbstreamType {
		//解压数据
		gzStr := fmt.Sprintf("mkdir -p %v &&  %v  -x -C %v < %v ", unxbStreamDataDir, xbStreamBin, unxbStreamDataDir, rsDataPath)
		_, _, err := common.SyncExecShell(gzStr)
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell unzip failed,err=[%v],shell=[%v]", err, gzStr)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	} else {
		//解压数据
		gzStr := fmt.Sprintf("mkdir -p %v && tar -xvf %v -C %v ", targetPath, rsDataPath, targetPath)
		_, _, err := common.SyncExecShell(gzStr)
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell unzip failed,err=[%v],shell=[%v]", err, gzStr)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	}

	// 解压完成后对已下载的包进行删除
	err = os.Remove(rsDataPath)
	if err != nil {
		common.Log.Warn("recovery data compression package failed to be deleted, err=[%v]", err)
	}
	var dataDir []string
	if !isXbstreamType {
		//修改数据目录
		dirList, err := ioutil.ReadDir(targetPath)
		if err != nil {
			errMsg := fmt.Sprintf("ReadDir failed,err=[%v]", err)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
		for _, fi := range dirList {
			if fi.IsDir() && fi.Name() != fileName {
				dataDir = append(dataDir, fi.Name())
			}
		}
		if dataDir == nil || len(dataDir) != 1 {
			errMsg := fmt.Sprintf("get dataDir failed,dataDir=[%v]", dataDir)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
		mvShell := fmt.Sprintf(" mv %v/%v/* %v", targetPath, dataDir[0], targetPath)
		_, _, err = common.SyncExecShell(mvShell)
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell mvShell failed,err=[%v],shell=[%v]", err, mvShell)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	}

	decompressStr := fmt.Sprintf("export PATH=$PATH:%v/bin && %v  --decompress --remove-original --parallel=16 %v/%v/",
		strings.Trim(curPath, " \r\n\t"), common.InnobackupexBinPath, targetPath, fileName)
	_, _, err = common.SyncExecShell(decompressStr)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell failed,err=[%v],shell=[%v]", err, decompressStr)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	var applylogStr string
	//执行应用操作
	if isFull {
		if end {
			applylogStr = fmt.Sprintf("%v --defaults-file=%s/%s/backup-my.cnf --apply-log  %s/%s --use-memory=5G ", common.InnobackupexBinPath,
				targetPath, fileName, targetPath, fileName)
		} else {
			applylogStr = fmt.Sprintf("%v --defaults-file=%s/%s/backup-my.cnf --apply-log --redo-only  %s/%s --use-memory=5G ", common.InnobackupexBinPath,
				targetPath, fileName, targetPath, fileName)
		}
	} else if end {
		applylogStr = fmt.Sprintf("%v  --defaults-file=%s/%v/backup-my.cnf --apply-log %s/base --use-memory=5G --incremental-dir=%s/%s", common.InnobackupexBinPath,
			targetPath, fileName, baseDir, targetPath, fileName)
	} else {
		applylogStr = fmt.Sprintf("%v  --defaults-file=%s/%v/backup-my.cnf --apply-log --redo-only %s/base --use-memory=5G --incremental-dir=%s/%s", common.InnobackupexBinPath,
			targetPath, fileName, baseDir, targetPath, fileName)
	}
	_, status, err := common.SyncExecShell(applylogStr)
	if err != nil || status != 0 {
		errMsg := fmt.Sprintf("SyncExecShell apply log failed. err=[%v] status=[%v] shell=[%v]", err, applylogStr, applylogStr)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	// 将增备恢复完成数据目录删除
	if !isFull {
		err = os.RemoveAll(targetPath)
		if err != nil {
			common.Log.Warn("incr recovery dara dir remove failed, err=[%v]", err)
			return nil
		}
	}
	return nil
}

//执行move back操作
func moveBackData(targetPath string) error {
	//修改cnf配置
	changeCnfshell := fmt.Sprintf("sed -i '/innodb_undo_directory=/d' %v/base/backup-my.cnf", targetPath)
	_, _, err := common.SyncExecShell(changeCnfshell)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell failed,err=[%v],shell=[%v]", err, changeCnfshell)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	changeCnfshell = fmt.Sprintf("sed -i '$adatadir=%v/var' %v/base/backup-my.cnf", targetPath, targetPath)
	_, _, err = common.SyncExecShell(changeCnfshell)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell failed,err=[%v],shell=[%v]", err, changeCnfshell)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//执行move back操作
	moveBack := fmt.Sprintf("%v --defaults-file=%v/base/backup-my.cnf --move-back %s/base", common.InnobackupexBinPath, targetPath, targetPath)
	_, _, err = common.SyncExecShell(moveBack)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell move back failed,err=[%v],shell=[%v]", err, moveBack)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	// moveback完成后对base目录删除
	removeDir := fmt.Sprintf("%v/base", targetPath)
	err = os.RemoveAll(removeDir)
	if err != nil {
		common.Log.Warn("remove base dir failed, err=[%v]", err)
		return nil
	}
	return nil
}

//启动mysql实例
func startInstance(isFdb bool, targetPath, dataPath string, taskMsg *pb_agent.MdcAgentAsyncMsg) (string, string, int, error) {
	//启动mysql实例
	var (
		instanceDir string
		err         error
		port        int
		passWord    string
	)

	if isFdb {
		//启动fdb实例
		port, instanceDir, err = installFdb(targetPath, dataPath)
	} else {
		//启动mysql57实例
		port, instanceDir, err = installMysql(targetPath, dataPath)
	}
	if err != nil {
		errMsg := fmt.Sprintf("install instance failed,err=[%v],task_id=[%v],cluster_id=[%v]", err, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
		common.Log.Warn(errMsg)
		return "", "", -1, errors.New(errMsg)
	}

	//生成账号并授权
	passWord, err = createUserAndGrant(instanceDir, taskMsg.GetExecRestore().RsTableName, taskMsg.GetExecRestore().AccountUser)
	if err != nil {
		errMsg := fmt.Sprintf("createUserAndGrant failed,err=[%v],task_id=[%v],cluster_id=[%v]", err, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
		common.Log.Warn(errMsg)
		return "", "", -1, errors.New(errMsg)
	}
	return passWord, instanceDir, port, nil
}

//安装mysql57
func installMysql(targetPath, dataPath string) (int, string, error) {
	var port int
	for idx := 0; idx < common.MAXPROCESSCOUNT; idx++ {
		port = makeMysqlPort()
		if port == -1 {
			errMsg := fmt.Sprintf("get mysql port fail, port=[%v]", port)
			common.Log.Error(errMsg)
			return -1, "", errors.New(errMsg)
		}
		portCheckCMD := fmt.Sprintf("netstat -tunl | grep -w %v | wc -l", port)
		status, _, err := common.SyncExecShell(portCheckCMD)
		if err != nil || strings.Trim(status, " \r\n\t") != "0" {
			errMsg := fmt.Sprintf("SyncExecShell exec install mysql failed,err=[%v],shell=[%v],status=[%v]", err, portCheckCMD, status)
			common.Log.Warn(errMsg)
			continue
		} else {
			break
		}
	}
	//生成配置文件
	err := generateTmplFile(common.ConfTeplPath, common.LocalInstallConfDir, targetPath, dataPath, port)
	if err != nil {
		errMsg := fmt.Sprintf("generate  Mysql conf file fail, err=[%v]", err)
		common.Log.Error(errMsg)
		return -1, "", err
	}
	//生成非通用的install脚本 和 sql
	baseDir, err := generateInstallFile(common.ConfTeplPath, common.LocalInstallConfDir, targetPath, port)
	if err != nil {
		errMsg := fmt.Sprintf("generate  install conf file fail,err=[%v]", err)
		common.Log.Error(errMsg)
		return -1, "", err
	}

	//拷贝install目录下安装脚本和授权sql到远程目录
	cmd := fmt.Sprintf("mkdir -p %v && cp %v/install/* %v && cp %v/general/* %v",
		targetPath, common.LocalInstallConfDir, targetPath, common.LocalInstallConfDir, targetPath)
	_, _, err = common.SyncExecShell(cmd)
	if err != nil {
		errMsg := fmt.Sprintf("cp mysql conf and install_mysql.sh fail,cmd=[%v],err=[%v]", cmd, err)
		common.Log.Error(errMsg)
		return -1, "", err
	}

	//执行安装启动脚本
	cmd = fmt.Sprintf("cd %v && bash install_mysql.sh", targetPath)
	_, _, err = common.SyncExecShell(cmd)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell exec install mysql failed,err=[%v],shell=[%v]", err, cmd)
		common.Log.Warn(errMsg)
		return -1, "", err
	}
	return port, baseDir, err
}

func makeMysqlPort() int {
	rand.Seed(time.Now().Unix())
	// 1024到49151，它们松散地绑定于一些服务
	port := rand.Intn(8000) + 1025
	return port
}

//获取路径下文件
func getDirTmplList(dirTmplPath string) (tmplList []string, err error) {
	fileInfo, err := ioutil.ReadDir(dirTmplPath)
	tmplList = make([]string, 0, len(fileInfo))
	if err != nil {
		common.Log.Error("read dir fail dir=[%v] err=[%v]", dirTmplPath, err)
		return nil, err
	}
	for _, fi := range fileInfo {
		if fi.IsDir() {
			continue
		}
		tmplList = append(tmplList, fi.Name())
	}
	return tmplList, nil
}

//生成线上mysql通用模板,confTmplPath/confPathle配置模板/生成配置目录
func generateTmplFile(confTmplPath string, confPath string, targetPath, dataPath string, port int) error {
	//生成mysql配置文件
	tmplIns := common.TmplInstance{
		Port:    port,
		BaseDir: fmt.Sprintf("%v/mysql", targetPath),
		DataDir: dataPath,
	}

	//返回genral节点下配置文件列表
	tmplList, err := getDirTmplList(confTmplPath + "/general")
	if err != nil {
		common.Log.Error("getDirTmplList fail,err=[%v]", err)
		return err
	}
	//创建general目录
	generalConfPath := fmt.Sprintf("%v/general/", confPath)
	err = os.MkdirAll(generalConfPath, 0777)
	if err != nil {
		common.Log.Warn("Fail to mkdir %v,reason=[%v]", generalConfPath, err)
		return err
	}

	//生成general配置文件
	for _, v := range tmplList {
		if strings.HasSuffix(v, ".tmpl") {
			err := common.GenerateConf(fmt.Sprintf("%v/general/%v", confTmplPath, v), fmt.Sprintf("%v/%v", generalConfPath, strings.Replace(v, ".tmpl", "", -1)), tmplIns)
			if err != nil {
				common.Log.Error("generate %v error. err=[%v]", v, err)
				return err
			}
		}
	}
	return nil
}

func generateInstallFile(confTmplPath string, confPath string, targetPath string, port int) (string, error) {
	rand.Seed(time.Now().Unix())
	serverId := rand.Intn(65535)
	//替换模板的结构体
	tmplIns := common.TmplInstance{
		BaseDir:     fmt.Sprintf("%v/mysql", targetPath),
		TmpBaseDir:  fmt.Sprintf("%v_mysql_%v", common.RemoteInstallDir, time.Now().Format("20060102")),
		ServerId:    int64(serverId),
		DownloadUrl: common.Config.MysqlNormalPackage,
		Port:        port,
		DataDir:     targetPath,
	}
	installFilePath := fmt.Sprintf("%v/install", confPath)
	err := os.MkdirAll(installFilePath, 0777)
	if err != nil {
		common.Log.Warn("Fail to mkdir %v,reason=[%v]", installFilePath, err)
		return "", err
	}
	//开始生成 install脚本
	err = common.GenerateConf(fmt.Sprintf("%v/shell/install_mysql.sh.tmpl", confTmplPath), fmt.Sprintf("%v/install_mysql.sh", installFilePath), tmplIns)
	if err != nil {
		common.Log.Error("generate  instal_mysql.sh  failed. err=[%v] ", err)
		return "", err
	}
	return tmplIns.BaseDir, nil
}

//安装fdb
func installFdb(targetPath, dataPath string) (int, string, error) {
	//获取fdb tar包
	wgetShell := fmt.Sprintf("cd %v && %v && tar -zxf mysql.tar.gz", targetPath, common.Config.MysqlFdbPackage)
	_, _, err := common.SyncExecShell(wgetShell)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell exec wget fdb mysql failed,err=[%v],shell=[%v]", err, wgetShell)
		common.Log.Warn(errMsg)
		return 0, "", err
	}
	//执行安装操作
	var port int
	for idx := 0; idx < common.MAXPROCESSCOUNT; idx++ {
		rand.Seed(time.Now().Unix())
		port = rand.Intn(65535)
		testShell := fmt.Sprintf("netstat -tunl | grep -w %v | wc -l", port)
		status, _, err := common.SyncExecShell(testShell)
		if err != nil || strings.Trim(status, " \r\n\t") != "0" {
			errMsg := fmt.Sprintf("SyncExecShell exec install mysql failed,err=[%v],shell=[%v],status=[%v]", err, testShell, status)
			common.Log.Warn(errMsg)
			continue
		} else {
			break
		}
	}
	installShell := fmt.Sprintf("cd %v/mysql && ./bin/mysql_server_init -b %v/mysql -d %v -p %v --innodb-bp-sz %v --default-character-set %v",
		targetPath, targetPath, dataPath, port, common.FdbInnodbbufferPool, common.FdbCharset)
	_, _, err = common.SyncExecShell(installShell)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell exec install fdb mysql failed,err=[%v],shell=[%v]", err, installShell)
		common.Log.Warn(errMsg)
		return 0, "", err
	}

	//校验是否启动成功
	checkShell := fmt.Sprintf("%v/bin/mysql --defaults-file=etc/user.root.cnf -NBe \"select 0;\"", targetPath)
	status, _, err := common.SyncExecShell(checkShell)
	if err != nil || strings.Trim(status, " \r\n\t") == "0" {
		errMsg := fmt.Sprintf("SyncExecShell exec check fdb install failed,err=[%v],shell=[%v]", err, installShell)
		common.Log.Warn(errMsg)
		return 0, "", err
	}

	return port, fmt.Sprintf("%v/mysql", targetPath), nil
}

//创建恢复实例对应的账户、密码并添加授权
func createUserAndGrant(dataPath string, tableName string, username string) (string, error) {
	var execSql string
	//生成创建账号语句
	password := common.RandString(common.PasswordLen)
	//生成授权语句
	err, grantStrList := genGrantStr(tableName, username, password)
	if err != nil {
		errStr := fmt.Sprintf("genGrantStr failed. tableName=[%v]",
			tableName)
		common.Log.Warn(errStr)
		return "", errors.New(errStr)
	}
	//生成sql
	if grantStrList != nil || len(grantStrList) > 0 {
		for _, val := range grantStrList {
			execSql = execSql + val
		}
	}
	//执行sql
	execSqlStr := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"%v\"", dataPath, dataPath, execSql)
	_, _, err = common.SyncExecShell(execSqlStr)
	if err != nil {
		errStr := fmt.Sprintf("SyncExecShell exec sql failed. err=[%v],shell=[%v]", err, execSqlStr)
		common.Log.Warn(errStr)
		return "", errors.New(errStr)
	}

	return password, nil
}

//生成授权语句
func genGrantStr(tableName string, username string, password string) (error, []string) {
	if "" == username || "" == password {
		errStr := fmt.Sprintf("Illegal parameter passed in. please check. username=[%v] password=[%v]", username, password)
		common.Log.Warn(errStr)
		return errors.New(errStr), nil
	}

	var tableNameList, grantSqlInfoList []string
	if tableName != "" {
		tableNameList = strings.Split(tableName, ",")
	}
	symbol := "%"
	//leadSymbol := "`"

	if len(tableNameList) > 0 {
		for _, moduleName := range tableNameList {
			//grantStr := fmt.Sprintf("grant select on %v%v%v%v.* to %v@'%v' identified by '%v';",
			//	leadSymbol, moduleName, symbol, leadSymbol, username, symbol, password)
			grantStr := fmt.Sprintf("grant select on %v to %v@'%v' identified by '%v';", moduleName, username, symbol, password)
			grantSqlInfoList = append(grantSqlInfoList, grantStr)
		}
	} else {
		grantStr := fmt.Sprintf("grant select on *.* to %v@'%v' identified by '%v';", username, symbol, password)
		grantSqlInfoList = append(grantSqlInfoList, grantStr)
	}

	return nil, grantSqlInfoList
}

//获得实例的gtid_set
func getGtidSet(instancePath string) (error, string) {
	//获得gtidSet
	gtidSetShell := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"show slave status\\G\"|tr -d '\\n'|sed 's/: /:/g'|sed 's/ /\\n/g '|grep %v|sed 's/[^:]*://'",
		instancePath, instancePath, common.ExecutedGtidSet)
	gtidSet, _, err := common.SyncExecShell(gtidSetShell)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell gtidSetShell failed,err=[%v],shell=[%v]", err, gtidSetShell)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), ""
	}
	return nil, strings.Trim(gtidSet, " \n\r\t")
}

//获取实例主库实例UUID
func getMasterUUID(instancePath string) (error, string) {
	// 获取主库UUID
	masterGtidShell := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"show slave status\\G\"|tr -d '\\n'|sed 's/: /:/g'|sed 's/ /\\n/g '|grep %v|sed 's/[^:]*://'",
		instancePath, instancePath, common.MasterUUID)

	masterUUID, _, err := common.SyncExecShell(masterGtidShell)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell masterGtidShell failed,err=[%v],shell=[%v]", err, masterUUID)
		common.Log.Warn(errMsg)
		return errors.New(errMsg), ""
	}

	return nil, strings.Trim(masterUUID, " \n\r\t")
}

//根据备份静止点生成实例masterUUID静止gtid集合
func formatGtidSet(instanceGtidSet, masterUUID string) (error, string) {
	var masterGtidSet string

	for _, gtidInfo := range strings.Split(instanceGtidSet, ",") {
		gtidUUID := strings.Split(gtidInfo, ":")[0]
		// 当前gtidUUID是否为masterUUID
		if gtidUUID == masterUUID {
			maxValues := strings.Split(strings.Split(gtidInfo, ":")[1], "-")[1]
			masterGtidSet = fmt.Sprintf("%v:%v", gtidUUID, maxValues)
		}
	}
	if masterGtidSet == "" {
		errMsg := fmt.Sprintf("Failed to format the GTID set masterGtidSet=[%v]", masterGtidSet)
		common.Log.Error(errMsg)
		return errors.New(errMsg), ""
	}

	return nil, masterGtidSet
}

//恢复数据到具体时间点
func rsSpecificTimeData(instanceDir, incDataEndTime, rsTime string, binlogDataPath []string, taskMsg *pb_agent.MdcAgentAsyncMsg) error {
	//若存在 获得当前位点
	err, instanceGtid := getGtidSet(instanceDir)
	if err != nil {
		errMsg := fmt.Sprintf("getGtidSet failed,err=[%v],task_id=[%v],cluster_id=[%v]", err, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	// 获取主库UUID信息
	err, masterUUID := getMasterUUID(instanceDir)
	if err != nil {
		errMsg := fmt.Sprintf("getMaster uuid failed,err=[%v],task_id=[%v],cluster_id=[%v]", err, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	// 格式化处理备份位点最后gtid集合
	err, gtidRes := formatGtidSet(instanceGtid, masterUUID)
	if err != nil {
		errMsg := fmt.Sprintf("format uuid failed,err=[%v],task_id=[%v],cluster_id=[%v]", err, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}

	//解析binlog文件找到开始应用的binlog文件
	startIdx, stopIdx := getStartBinlogIdx(binlogDataPath, gtidRes, instanceDir, masterUUID, rsTime)
	if startIdx < 0 {
		errMsg := fmt.Sprintf("getStartBinlogIdx failed,idx=[%v],task_id=[%v],cluster_id=[%v]", startIdx, taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	//应用binlog文件
	if err = applyBinlogFile(binlogDataPath[startIdx:stopIdx], instanceDir, incDataEndTime, rsTime); err != nil {
		errMsg := fmt.Sprintf("applyBinlogFile failed,task_id=[%v],cluster_id=[%v],err=[%v]", taskMsg.TaskId, taskMsg.GetBaseMessage().ClusterId, err)
		common.Log.Warn(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

//找到需要开始应用的binlog文件
func getStartBinlogIdx(binlogDataPath []string, instanceGtid, instancePath, masterUUID, rsTime string) (int, int) {
	startIdx, stopIdx := checkSubGtidSet(binlogDataPath, instanceGtid, instancePath, masterUUID, rsTime)
	return startIdx, stopIdx
}

//判断binglog文件中gtid_set是否是当前实例的子集
func checkSubGtidSet(binlogDataPath []string, instanceGtid, instancePath, masterUUID, rsTime string) (int, int) {
	var (
		previousEvent     *replication.PreviousGTIDsEvent
		eventHeaderTime   uint32
		startIdx, stopIdx int
	)
	//解析binlog获得previousEvent 找到对应的GtidSet
	binlogPraser := replication.NewBinlogParser()
	for idx, binlogFile := range binlogDataPath {
		binlogPraser.ParseFile(binlogFile, 4, func(event *replication.BinlogEvent) error {
			if event.Header.EventType == replication.PREVIOUS_GTIDS_EVENT {
				eventHeaderTime = event.Header.Timestamp
				previousEvent = event.Event.(*replication.PreviousGTIDsEvent)
			}
			return nil
		})

		if previousEvent == nil {
			errMsg := fmt.Sprintf("get previousGtidEvent of binlogFile failed,fileName=[%v]", binlogDataPath)
			common.Log.Warn(errMsg)
			return -1, -1
		}

		err, gtidSet := formatBinlogGtidSet(previousEvent.GTIDSets, masterUUID)
		if err != nil {
			return -1, -1
		}
		//比较 gtidSet;备份集合gtid集合是否在当前binlog文件子集
		sql := fmt.Sprintf("select gtid_subset('%v','%v') as gtid_subset;", instanceGtid, gtidSet)
		compareGtidSetShell := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -NBe \"%v\"",
			instancePath, instancePath, sql)
		gtidTag, _, err := common.SyncExecShell(compareGtidSetShell)
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell compareGtidSetShell failed,err=[%v],fileName=[%v],shell=[%v]", err, binlogDataPath, compareGtidSetShell)
			common.Log.Warn(errMsg)
			return -1, -1
		}
		gtidSubset, err := strconv.Atoi(strings.Trim(gtidTag, " \n\r\t"))
		if err != nil {
			errMsg := fmt.Sprintf("atoi gtidSubset failed,gtidSubset=[%v]", gtidSet)
			common.Log.Warn(errMsg)
			return -1, -1
		}
		// binlog文件gtid集合于实例集合存在交集
		if gtidSubset == 1 {
			eventTime := time.Unix(int64(eventHeaderTime), 0)
			if eventTime.Format(common.TIME_FORMAT) <= rsTime && idx != 0 {
				startIdx = idx - 1
			} else if eventTime.Format(common.TIME_FORMAT) > rsTime {
				stopIdx = idx
			}
		}
		// 确认解析文件起始位置
		if startIdx != 0 && stopIdx != 0 {
			return startIdx, stopIdx
		}
	}
	return -1, -1
}

// 格式化binlog文件gtid集合
func formatBinlogGtidSet(fileSet, masterUUID string) (error, string) {
	var gtidRes string

	for _, gtidInfo := range strings.Split(fileSet, ",") {
		gtidUUID := strings.Split(gtidInfo, ":")[0]
		// 当前gtidUUID是否为masterUUID
		if gtidUUID == masterUUID {
			gtidRes = gtidInfo
		}
	}
	if gtidRes == "" {
		errMsg := fmt.Sprintf("Failed to format the binlog GTID set gtidInfo=[%v]", gtidRes)
		common.Log.Error(errMsg)
		return errors.New(errMsg), ""
	}
	return nil, gtidRes
}

//应用binlog文件
func applyBinlogFile(binlogDataPath []string, instanceDir, startTime, endTime string) error {
	for _, file := range binlogDataPath {
		applyBinlogSql := fmt.Sprintf("%v/bin/mysqlbinlog --stop-datetime='%s' %s| mysql --defaults-extra-file=%v/etc/user.root.cnf",
			instanceDir, endTime, file, instanceDir)
		_, _, err := common.SyncExecShell(applyBinlogSql)
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell apply for binlog,err=[%v],shell=[%v]", err, applyBinlogSql)
			common.Log.Warn(errMsg)
			return errors.New(errMsg)
		}
	}
	return nil
}

//处理获得备库信息的请求
func HandleGetBkMysqlInfo(taskMsg *pb_agent.MdcAgentSyncMsg) (*pb_agent.GetBkMysqlInfoRes, error) {
	//获得mysql从库实例的账户和密码
	bkPath := taskMsg.GetGetBkMysqlInfo().BkMysqlPath
	remoteIp := taskMsg.GetGetBkMysqlInfo().RemoteIp
	if bkPath == "" || remoteIp == "" {
		errMsg := fmt.Sprintf("Invalid parameter failed,msg=[%v]", taskMsg)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	//获得备库账户
	userShell := fmt.Sprintf("cat %v/etc/user.root.cnf | awk -F \"=\" '{if($1==\"user\")print $2}'",
		bkPath)
	user, _, err := common.SyncExecShell(userShell)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell get user failed,err=[%v],shell=[%v]", err, userShell)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	//获得备库密码
	passwordShell := fmt.Sprintf("cat %v/etc/user.root.cnf | awk -F \"=\" '{if($1==\"password\")print $2}'",
		bkPath)
	password, _, err := common.SyncExecShell(passwordShell)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell get password failed,err=[%v],shell=[%v]", err, passwordShell)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	//授权
	grantStr := fmt.Sprintf("grant select on *.* to %v@'%v' identified by '%v';", strings.Trim(user, " \n\r\t"), strings.Trim(remoteIp, "\n\r\t"), strings.Trim(password, " \n\r\t"))
	//执行sql
	execSqlStr := fmt.Sprintf("%v/bin/mysql --defaults-extra-file=%v/etc/user.root.cnf -e \"%v\"", bkPath, bkPath, grantStr)
	_, _, err = common.SyncExecShell(execSqlStr)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell exec sql failed. err=[%v]，shell=[%v]", err, execSqlStr)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	response := &pb_agent.GetBkMysqlInfoRes{
		BkUser:     strings.Trim(user, " \n\r\t"),
		BkPassword: strings.Trim(password, " \n\r\t"),
	}
	return response, nil
}

//处理解析binlog任务
func HandleExecParseBinlog(taskMsg *pb_agent.MdcAgentAsyncMsg) (res *pb_server.ExecParseBinlogReport, err error) {
	startTime := taskMsg.GetExecParseBinlog().StartTime
	endTime := taskMsg.GetExecParseBinlog().Endtime
	binlogPath := taskMsg.GetExecParseBinlog().BinlogPath
	bkIp := taskMsg.GetExecParseBinlog().BkIp
	bkPort := taskMsg.GetExecParseBinlog().BkPort
	bkPath := taskMsg.GetExecParseBinlog().BkPath
	user := taskMsg.GetExecParseBinlog().User
	password := taskMsg.GetExecParseBinlog().Password
	if startTime == "" || endTime == "" || binlogPath == nil || bkIp == "" || bkPath == "" || bkPort <= 0 || user == "" || password == "" {
		errMsg := fmt.Sprintf("Invalid parameter,msg=[%v]", taskMsg)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	//解析binlog文件并获得sql
	parseFileParams := binlog2sql.ParseFileParams{
		Host:      bkIp,
		Port:      strconv.FormatInt(bkPort, 10),
		User:      user,
		Passwd:    password,
		StartTime: startTime,
		EndTime:   endTime,
		FileList:  binlogPath,
	}
	sqlFile, err := binlog2sql.ParseFile(parseFileParams, taskMsg.TaskId)
	if err != nil {
		errMsg := fmt.Sprintf("ParseFile failed,err=[%v]", err)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	//上传bos
	objName, err := bos.UploadObjToBos(sqlFile, taskMsg.TaskId)
	if err != nil {
		errMsg := fmt.Sprintf("Failed to UploadObjToBos. err=[%v]", err)
		common.Log.Warn(errMsg)
		return nil, errors.New(errMsg)
	}
	res = &pb_server.ExecParseBinlogReport{
		BosPath: objName,
	}
	return res, err
}

func restorePartition(restorePartitionConfig string, basePath, taskId string) (err error, partition string) {

	var (
		wg           sync.WaitGroup
		bosMetaSlice []UploadBosMeta
		lock         sync.Mutex
	)
	partitionConfig := new(global.RestorePartitionConfig)
	err = json.Unmarshal([]byte(restorePartitionConfig), partitionConfig)
	if err != nil {
		common.Log.Info("can't Unmarshal restorePartitionConfig. restorePartitionConfig=[%v]", restorePartitionConfig)
		return err, ""
	}
	//  info=[{DbList:[testHashMonthDbTable_3 testRangeDb_02] BaseVarPath:/home/<USER>/biubiu124/sourceData/base FilePath:/home/<USER>/biubiu124/sourceData/ DirName:var0 BosPath:/RestorePartition/494/}]
	bosMetaSlice = make([]UploadBosMeta, len(partitionConfig.DBMapping))
	for index, config := range partitionConfig.DBMapping {
		r := common.RestorePartitionToBos{
			DbList: config.DBList,
			// /home/<USER>/biubiu124/sourceData/base/var
			BaseVarPath: fmt.Sprintf("%v/var", basePath),
			// /home/<USER>/biubiu124/sourceData/base
			FilePath: basePath,
			DirName:  fmt.Sprintf("var%v", index),
			BosPath:  fmt.Sprintf("%v%v/", common.RestoreBosPrefix, taskId),
		}
		mappingIndex := index
		wg.Add(1)
		// 上传bos
		go func() {
			defer wg.Done()
			common.Log.Info("[restorePartition] start db mapping pack and upload to bos. info=[%+v]", r)
			tmpBosMeta := uploadDBtoBosPath(r)
			lock.Lock()
			defer lock.Unlock()
			bosMetaSlice[mappingIndex] = tmpBosMeta
			common.Log.Info("[restorePartition] update uploadBosPath to memory uploadBosPath. info=[%+v]", r)
		}()
	}
	wg.Wait()
	// 若bos目录为空，则未上传完成。
	for index, meta := range bosMetaSlice {
		if meta.UploadBosPath == "" {
			return fmt.Errorf("uploadBosPath can't empty。 index=[%v]  partitionConfig.DBMapping=[%+v] ",
				index, partitionConfig.DBMapping), ""
		}
		partitionConfig.DBMapping[index].BosUploadPath = meta.UploadBosPath
		partitionConfig.DBMapping[index].BosDirMeta.FileCount = meta.UploadBosFileCount
	}
	// 打包上传var的目录
	r := common.RestorePartitionToBos{
		// /home/<USER>/biubiu124/sourceData/base/var
		BaseVarPath: fmt.Sprintf("%v/var", basePath),
		// /home/<USER>/biubiu124/sourceData/base
		FilePath: basePath,
		DirName:  "var",
		BosPath:  fmt.Sprintf("%v%v/", common.RestoreBosPrefix, taskId),
	}
	varBosMeta := uploadDBtoBosPath(r)
	if varBosMeta.UploadBosPath == "" {
		return fmt.Errorf("varBosUploadPath can't empty。partitionConfig.DBMapping=[%+v] ", partitionConfig.DBMapping), ""
	}
	partitionConfig.VarBosUploadPath = varBosMeta.UploadBosPath
	partitionConfig.VarBosDirMeta.FileCount = varBosMeta.UploadBosFileCount
	partitionConfigByte, err := json.Marshal(partitionConfig)
	if err != nil {
		common.Log.Error("can't Marshal partitionConfig. partitionConfig=[%+v]", partitionConfig)
		return err, ""
	}
	// bos底层元数据使用mysql， 主从同步存在ms级别延迟, 通过sleep暂时规避。
	time.Sleep(10 * time.Second)

	return nil, string(partitionConfigByte)
}

// 1.将目录文件移动至指定文件夹
// 2.将整个目录下文件上传至bos
func uploadDBtoBosPath(r common.RestorePartitionToBos) (meta UploadBosMeta) {
	common.Log.Info("start to tar and upload partition var to bos. info=[%+v]", r)
	targetDir := fmt.Sprintf("%v/%v/", r.FilePath, r.DirName)
	output, status, err := common.SyncExecShell(fmt.Sprintf("mkdir -p %v", targetDir))
	if status != 0 || err != nil {
		errMsg := fmt.Sprintf("[RestorePartition] mkdir failed. output=[%v] status=[%v] err=[%v] mkdirShell=[%v]", output, status, err, targetDir)
		common.Log.Error(errMsg)
		return meta
	}
	// 移动dbList至指定文件夹
	for _, s := range r.DbList {
		mvShell := fmt.Sprintf("mv %v/%v %v ", r.BaseVarPath, s, targetDir)
		output, status, err = common.SyncExecShell(mvShell)
		if status != 0 || err != nil {
			errMsg := fmt.Sprintf("[RestorePartition] mv dbList failed. output=[%v] status=[%v] err=[%v] mvShell=[%v]", output, status, err, mvShell)
			common.Log.Error(errMsg)
			return meta
		}
	}
	// 上传此文件
	bosFilePath := fmt.Sprintf("%v%v", r.BosPath, r.DirName)
	localFilePath := fmt.Sprintf("%v/%v", r.FilePath, r.DirName)
	err = common.Do(
		func() error {
			return bos.BosCmdSyncDirToBos(common.Config.DbaBosBucket, localFilePath, bosFilePath)
		},
		5,
		5*time.Second,
	)
	if err != nil {
		common.Log.Error("upload data to bos failed. err=[%v]", err)
	}
	// 统计文件数量
	count, err := bos.GetLocalDirFileCount(localFilePath)
	if err != nil {
		common.Log.Warning("get local file count failed. err=[%v] count=[%v]", err, count)
	}
	common.Log.Info("upload partition var to bos successfully. info=[%+v] bosFilePath=[%v]", r, bosFilePath)
	meta.UploadBosPath = bosFilePath
	meta.UploadBosFileCount = count
	return meta
}
