package worker

import (
	"dt-common/global"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"math/rand"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"

	"mdc-agent/common"
	"mdc-agent/library/bos"
	"mdc-agent/library/logger"
	"mdc-agent/library/utils"
)

type XtraExecResult struct {
	dataSize string
	lsnFrom  string
	lsnTo    string
	endTime  string
}

// 处理xtra备份任务
func HandlExecxtra(taskMsg *pb_agent.MdcAgentAsyncMsg) (*pb_server.ExecXtrabkReport, error) {
	lsnPos := taskMsg.GetStartXtrabkExecute().GetBkLsnPos()
	remoteIp := taskMsg.GetStartXtrabkExecute().GetRemoteIp()
	remotePath := taskMsg.GetStartXtrabkExecute().GetRemotePath()
	baseDir := taskMsg.GetStartXtrabkExecute().GetBkBasedir()
	user := taskMsg.GetStartXtrabkExecute().GetBkUser()
	password := taskMsg.GetStartXtrabkExecute().GetBkPassword()
	bkOpt := taskMsg.GetStartXtrabkExecute().GetBkopt()
	bkOpt = strings.Trim(bkOpt, " ")
	extraLsndir := fmt.Sprintf("./extra_lsndir/%v", taskMsg.GetTaskId())
	logger.Debug("[HandlExecxtra] taskMsg=[%+v]", *taskMsg.GetStartXtrabkExecute())
	if lsnPos == "" || remoteIp == "" || remotePath == "" || baseDir == "" || user == "" || password == "" {
		errMsg := fmt.Sprintf("Invalid parameter,lsnPos=[%v],remoteIp=[%v],remotePath=[%v],baseDir=[%v],user=[%v],password=[%v]", lsnPos, remoteIp, remotePath, baseDir, user, password)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	var xtraStr string
	//判断备份进程是否存在
	getXtraPid := fmt.Sprintf("ps -ef | grep \"innobackupex --defaults-file=%v/etc/my.cnf\"|grep -v grep |awk '{print $2}'", baseDir)
	pid, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", getXtraPid},
		Timeout: 5 * time.Second,
		Retry:   3,
	})
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell getXtraPid failed,err=[%v],shell=[%v]", err, getXtraPid)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	if pid != "" {
		errMsg := fmt.Sprintf("innobackupex is running,err=[%v]", err)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	remoteFile := fmt.Sprintf("%v.%v", remotePath, common.XbstreamType)
	dateDir, _ := filepath.Split(remotePath)
	//判断xtra目标备份文件是否存在, 若存在报错退出
	checkPathShell := fmt.Sprintf("ssh -i %v -o StrictHostKeyChecking=no mysql@%s \"! [ -e %v ] && echo -n checkpass && mkdir -p %v \" ",
		common.DbbkRsaPath, remoteIp, remoteFile, dateDir)
	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", checkPathShell},
		Timeout: 5 * time.Second,
		Retry:   3,
	})
	if err != nil || output != "checkpass" {
		errMsg := fmt.Sprintf("SyncExecShell detect back file failed. err=[%v] shell=[%v] output=[%v]", err, checkPathShell, output)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	_, err = utils.ExecCommand(&utils.Shell{
		Command: "mkdir",
		Args:    []string{"-p", extraLsndir},
		Timeout: 5 * time.Second,
		Retry:   3,
	})
	if err != nil {
		errMsg := fmt.Sprintf("create extraLsndir failed. err=[%v]", err)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	//判断备库实例是否开启并行复制,slave_parallel_workers=0
	worekers := fmt.Sprintf("%s/bin/mysql --defaults-file=%s/etc/user.root.cnf -e \"show variables like 'slave_parallel_workers';\"|grep 'slave_parallel_workers'|awk '{print $2}'", baseDir, baseDir)
	nums, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", worekers},
		Timeout: 5 * time.Second,
		Retry:   3,
	})
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell get  slave_parallel_workers failed. err=[%v] shell=[%v]", err, worekers)
		logger.Error("[HandlExecxtra] xtrabackup failed. errMsg=[%v] output=[%v]", errMsg, nums)
		return nil, errors.New(errMsg)
	}

	nums = strings.Trim(nums, " \n\r\t")
	if nums == "0" {
		logger.Info("parallel replication is not enabled. shell=[%v]", worekers)
		//开启xtra备份
		if lsnPos == common.InitLsnPos {
			//执行全备
			// 同步执行
			xtraStr = fmt.Sprintf("%v --defaults-file=%s/etc/my.cnf --user=%s --password=\"%s\" --kill-long-query-type=select --kill-long-queries-timeout=\"%s\"   --socket=%s/tmp/mysql.sock --no-timestamp --slave-info --extra-lsndir=%v --stream=xbstream --compress %s --compress-threads=4 --throttle=500  --backup %v |"+
				"ssh -i %v  -o StrictHostKeyChecking=no mysql@%s \"cat - >  %v \"",
				common.InnobackupexBinPath, baseDir, user, password, common.Config.KillLongQueriesTimeOut, baseDir, extraLsndir, bkOpt, baseDir, common.DbbkRsaPath, remoteIp, remoteFile)
		} else {
			//执行增备
			// 同步执行
			xtraStr = fmt.Sprintf("%v --defaults-file=%s/etc/my.cnf --user=%s --password=\"%s\"  --kill-long-query-type=select --kill-long-queries-timeout=\"%s\"  --socket=%s/tmp/mysql.sock  --no-timestamp --slave-info --extra-lsndir=%v --incremental --incremental-lsn=%s %s --stream=xbstream --compress --compress-threads=4 --throttle=500  --backup %s |"+
				"ssh -i %v  -o StrictHostKeyChecking=no mysql@%s \"cat - >  %v \"",
				common.InnobackupexBinPath, baseDir, user, password, common.Config.KillLongQueriesTimeOut, baseDir, extraLsndir, lsnPos, bkOpt, baseDir, common.DbbkRsaPath, remoteIp, remoteFile)
		}
		outputstr, err := utils.ExecCommand(&utils.Shell{
			Command: "bash",
			Args:    []string{"-c", xtraStr},
			Timeout: 5 * time.Hour,
			Retry:   3,
		})
		// 判断 status 和 err
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell exec xtra failed. err=[%v] shell=[%v]", err, xtraStr)
			logger.Error("[HandlExecxtra] xtrabackup failed. errMsg=[%v] output=[%v]", errMsg, outputstr)
			return nil, errors.New(errMsg)
		}
		// 备份完成
		logger.Info("xtrabackup success. output=[%v] shell=[%v]", outputstr, xtraStr)
	} else {
		//关闭并行复制
		replicationStop := fmt.Sprintf("%s/bin/mysql --defaults-file=%s/etc/user.root.cnf -e \"stop slave;set global slave_parallel_workers=0;set global slave_preserve_commit_order=0;start slave;\"", baseDir, baseDir)
		_, err := utils.ExecCommand(&utils.Shell{
			Command: "bash",
			Args:    []string{"-c", replicationStop},
			Timeout: 5 * time.Second,
			Retry:   3,
		})
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell replicationStop failed. err=[%v] shell=[%v]", err, replicationStop)
			logger.Error("[HandlExecxtra] xtrabackup failed. errMsg=[%v] output=[%v]", errMsg)
			return nil, errors.New(errMsg)
		}
		logger.Info("replicationStop succes. shell=[%v]", replicationStop)
		//开启xtra备份
		if lsnPos == common.InitLsnPos {
			//执行全备
			// 同步执行
			xtraStr = fmt.Sprintf("%v --defaults-file=%s/etc/my.cnf --user=%s --password=\"%s\" --kill-long-query-type=select  --kill-long-queries-timeout=\"%s\"  --socket=%s/tmp/mysql.sock --no-timestamp --slave-info --extra-lsndir=%v --stream=xbstream --compress %s --compress-threads=4 --throttle=500  --backup %v |"+
				"ssh -i %v  -o StrictHostKeyChecking=no mysql@%s \"cat - >  %v \"",
				common.InnobackupexBinPath, baseDir, user, password, common.Config.KillLongQueriesTimeOut, baseDir, extraLsndir, bkOpt, baseDir, common.DbbkRsaPath, remoteIp, remoteFile)
		} else {
			//执行增备
			// 同步执行
			xtraStr = fmt.Sprintf("%v --defaults-file=%s/etc/my.cnf --user=%s --password=\"%s\"  --kill-long-query-type=select --kill-long-queries-timeout=\"%s\"  --socket=%s/tmp/mysql.sock  --no-timestamp --slave-info --extra-lsndir=%v --incremental --incremental-lsn=%s %s --stream=xbstream --compress --compress-threads=4 --throttle=500  --backup %s |"+
				"ssh -i %v  -o StrictHostKeyChecking=no mysql@%s \"cat - >  %v \"",
				common.InnobackupexBinPath, baseDir, user, password, common.Config.KillLongQueriesTimeOut, baseDir, extraLsndir, lsnPos, bkOpt, baseDir, common.DbbkRsaPath, remoteIp, remoteFile)
		}
		outputstr, err := utils.ExecCommand(&utils.Shell{
			Command: "bash",
			Args:    []string{"-c", xtraStr},
			Timeout: 5 * time.Hour,
			Retry:   3,
		})
		// 判断 status 和 err
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell exec xtra failed. err=[%v] shell=[%v] ", err, xtraStr)
			logger.Error("[HandlExecxtra] xtrabackup failed. errMsg=[%v] output=[%v]", errMsg, outputstr)
			return nil, errors.New(errMsg)
		}
		// 备份完成
		logger.Info("xtrabackup success. output=[%v] shell=[%v]", outputstr, xtraStr)

		// 开启并行复制
		replicationStart := fmt.Sprintf("%s/bin/mysql --defaults-file=%s/etc/user.root.cnf -e \"stop slave;set global slave_parallel_workers=16;set global slave_preserve_commit_order=1;start slave;\"", baseDir, baseDir)
		_, err = utils.ExecCommand(&utils.Shell{
			Command: "bash",
			Args:    []string{"-c", replicationStart},
			Timeout: 5 * time.Second,
			Retry:   3,
		})
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell replicationStart failed. err=[%v] shell=[%v]", err, replicationStart)
			logger.Error("[HandlExecxtra] xtrabackup failed. errMsg=[%v] output=[%v]", errMsg)
			return nil, errors.New(errMsg)
		}
		logger.Info("replicationStart succes. shell=[%v]", replicationStart)
	}

	//获得执行完成后 xtra记录的信息
	err, xtraInfo := getXtraInfoAndDelete(extraLsndir, remoteIp, remoteFile)
	if err != nil {
		errMsg := fmt.Sprintf("getXtraInfo failed,err=[%v]", err)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	mdcResMsg := &pb_server.ExecXtrabkReport{
		XtraEndTime: xtraInfo.endTime,
		BkLsnFrom:   xtraInfo.lsnFrom,
		BkLsnTo:     xtraInfo.lsnTo,
		BkSize:      xtraInfo.dataSize,
		// 远程大磁盘存储的文件路径
		RemotePathFile: remoteFile,
	}
	return mdcResMsg, nil
}

// 定时监测xtra 备份状态
func detectXtraBackupStatus(baseDir, remoteIp, remotePath string) error {
	tick := time.NewTicker(time.Second * time.Duration(common.Config.AnalysisBnsTimeout))
	logger.Info("Start detectXtraBackupStatus")
	for {
		select {
		case <-tick.C:
			err, ok := checkXtraBackupStatus(baseDir, remoteIp, remotePath)
			if err != nil {
				errMsg := fmt.Sprintf("checkXtraBackupStatus failed,err=[%v]", err)
				logger.Error(errMsg)
				return errors.New(errMsg)
			}
			if ok {
				return nil
			}
		}
	}
}

// 判断xtra任务状态
func checkXtraBackupStatus(baseDir, remoteIp, remotePath string) (err error, ok bool) {
	/*
		针对秒级别备份结束来不及探测情况不再进行备份进程探测;
		//判断备份进程是否存在
		getXtraPid := fmt.Sprintf("ps aux | grep \"innobackupex --defaults-file=%v/etc/my.cnf\" | grep -v grep ", baseDir)
		pid, _, err := common.SyncExecShell(getXtraPid)
		if err != nil {
			logger.Warn("SyncExecShell getXtraPid failed,err=[%v],shell=[%v]", err, getXtraPid)
		}
		if pid != "" {
			logger.Notice("innobackupex is running")
		}

	*/

	// todo 针对备份一直未生成备份结束标志文件;增加时间周期探测进程处理
	//判断备份完成的文件是否存在
	checkFileShell := fmt.Sprintf("ssh -i %v -o StrictHostKeyChecking=no mysql@%s \"[ -f %v/xtrabackup_info.qp ] && echo yes || echo no\" ", common.DbbkRsaPath, remoteIp, remotePath)
	out, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", checkFileShell},
		Timeout: 5 * time.Second,
		Retry:   3,
	})
	if err != nil || strings.Trim(out, " \r\n\t") == "no" {
		errMsg := fmt.Sprintf("SyncExecShell checkFileShell failed,err=[%v],fileStatus=[%v],shell=[%v]", err, out, checkFileShell)
		logger.Error(errMsg)
		return errors.New(errMsg), false
	}

	return nil, true
}

// 获得备份完成时间、LSN等信息
func getXtraInfoAndDelete(extraLsndir, remoteIp, remotePath string) (error, *XtraExecResult) {
	result := new(XtraExecResult)
	//解压文件并获取备份相关信息
	xtraInfo, err := ioutil.ReadFile(fmt.Sprintf("%v/xtrabackup_info", extraLsndir))
	if err != nil {
		logger.Error("can't get xtrabckup info. err=[%v]", err)
		return err, nil
	}

	/*
		start_time = 2022-08-18 18:58:38
		end_time = 2022-08-18 18:58:45
		lock_time = 0
		binlog_pos = filename 'mysql-bin.003636', position '214340659', GTID of the last change '17346592-4a91-11eb-921a-b8599f310ebc:1-2312020820,
		ea404adb-4402-11eb-b932-b8599f18b2f8:1-1497823'
		innodb_from_lsn = 6540891893006
		innodb_to_lsn = 6564338052730
	*/
	toLSNRegexp := regexp.MustCompile(`innodb_to_lsn\s=\s([0-9]+)`)
	toLSNParams := toLSNRegexp.FindAllStringSubmatch(string(xtraInfo), -1)
	if len(toLSNParams) == 1 && len(toLSNParams[0]) == 2 {
		result.lsnTo = toLSNParams[0][1]
	}

	fromLSNRegexp := regexp.MustCompile(`innodb_from_lsn\s=\s([0-9]+)`)
	fromLSNParams := fromLSNRegexp.FindAllStringSubmatch(string(xtraInfo), -1)
	if len(fromLSNParams) == 1 && len(fromLSNParams[0]) == 2 {
		result.lsnFrom = fromLSNParams[0][1]
	}

	endTimeRegexp := regexp.MustCompile(`end_time\s=\s(.*)`)
	endTimeParams := endTimeRegexp.FindAllStringSubmatch(string(xtraInfo), -1)
	if len(endTimeParams) == 1 && len(endTimeParams[0]) == 2 {
		result.endTime = endTimeParams[0][1]
	}
	if result.endTime == "" || result.lsnTo == "" || result.lsnFrom == "" {
		return fmt.Errorf("get xtraInfo from xtrabackup_info failed. result=[%+v]", result), nil
	}
	//获得大小
	getSizeStr := fmt.Sprintf("ssh -i %v -o StrictHostKeyChecking=no mysql@%s \"ls -lh %s  \"|awk '{print $5}'",
		common.DbbkRsaPath, remoteIp, remotePath)
	dataSize, _, err := common.SyncExecShell(getSizeStr)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell get dataSize failed,infoslice=[%v],err=[%v],shell=[%v]", dataSize, err, getSizeStr)
		logger.Error(errMsg)
		return errors.New(errMsg), nil
	}
	result.dataSize = strings.Trim(dataSize, " \n\r\t")
	// 清理文件
	if extraLsndir != "" {
		_, _, err = common.SyncExecShell(fmt.Sprintf("rm -rf %v", extraLsndir))
		if err != nil {
			logger.Error("clean tmp dir failed. err=[%v]", err)
		}
	}

	return nil, result
}

// 处理上传dba bos任务
func HandleUpLoadDbaBos(taskMsg *pb_agent.MdcAgentAsyncMsg) (*pb_server.UploadDBABOSReport, error) {
	filePath := taskMsg.GetUploadDbaBos().DataDir
	bosPath := taskMsg.GetUploadDbaBos().DbaBosDir
	logger.Debug("[HandleUpLoadDbaBos] filePath=[%v] bosPath=[%v]", filePath, bosPath)
	if filePath == "" || bosPath == "" {
		errMsg := fmt.Sprintf("Invalid parameter,filePath=[%v],bosPath=[%v]", filePath, bosPath)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	_, dataFileName := filepath.Split(filePath)
	bosFilePath, _ := filepath.Split(bosPath)
	targetBosPath := fmt.Sprintf("%v%v", bosFilePath, dataFileName)
	logger.Debug("[HandleUpLoadDbaBos] filePath=[%v] targetBosPath=[%v]", filePath, targetBosPath)
	// 分块上传数据
	isSuccess, err := bos.BlockUploadToBos(filePath, targetBosPath)
	if !isSuccess || err != nil {
		errMsg := fmt.Sprintf("BlockUploadToBos failed,err =[%v],filePath=[%v],bosPath=[%v]", err, filePath, bosPath)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	mdcResMsg := &pb_server.UploadDBABOSReport{
		BosPathFile: targetBosPath,
		BosBucket:   common.Config.DbaBosBucket,
	}

	// 兼容新版本xbstream类型文件上传逻辑。
	if strings.Contains(filePath, common.XbstreamType) {
		binlogPath := fmt.Sprintf("%v.%v.%v", filePath, common.BinlogPathName, common.UnpackType)
		binlogBosPath := fmt.Sprintf("%v.%v.%v", targetBosPath, common.BinlogPathName, common.UnpackType)
		exist, errExist := PathExists(binlogPath)
		if errExist != nil {
			logger.Error("judge path exists failed. path=[%v] err=[%v]", binlogPath, err)
			return nil, err
		}
		logger.Debug("[HandleUpLoadDbaBos] binlogPath=[%v] binlogBosPath=[%v]", binlogPath, binlogBosPath)
		// 上传binlog数据
		if exist {
			isSuccess, err = bos.BlockUploadToBos(binlogPath, binlogBosPath)
			if !isSuccess || err != nil {
				errMsg := fmt.Sprintf("BlockUploadToBos failed. err =[%v] binlogPath=[%v] bosPath=[%v]", err, binlogPath, bosPath)
				logger.Error(errMsg)
				return nil, errors.New(errMsg)
			}
			mdcResMsg.BosBinlogFile = binlogBosPath
		}
	}
	return mdcResMsg, nil
}

// 处理停止备份任务
func HandleStopXtraTask(taskMsg *pb_agent.MdcAgentSyncMsg) error {
	//判断xtraback 进程是否存在
	getXtraPid := "ps -ef | grep \"innobackupex\"|grep -v grep |awk '{print $2}'"
	pid, _, err := common.SyncExecShell(getXtraPid)
	if err != nil || pid == "" {
		errMsg := fmt.Sprintf("SyncExecShell getXtraPid failed,err=[%v],shell=[%v]", err, getXtraPid)
		logger.Error(errMsg)
		return errors.New(errMsg)
	}
	//进程存在的话 kill进程
	stopXtraPid := fmt.Sprintf("kill -9 %s", strings.Trim(pid, " \n\r\t"))
	_, _, err = common.SyncExecShell(stopXtraPid)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell stopXtraPid failed,err=[%v],shell=[%v]", err, stopXtraPid)
		logger.Error(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 处理开启binlog备份任务
func HandleStartBinlogTask(taskMsg *pb_agent.MdcAgentSyncMsg) (*pb_agent.StartBinlogRes, error) {
	binlogBkPath := taskMsg.GetExecStartBinlog().BkBinlogDir
	bkMysqlPath := taskMsg.GetExecStartBinlog().BkMysqlPath
	if binlogBkPath == "" || bkMysqlPath == "" {
		errMsg := fmt.Sprintf("Invalid parameter,bkMysqlPath=[%v],binlogBkPath=[%v]", bkMysqlPath, binlogBkPath)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	//获得从库连接主库的账户、密码等信息
	err, masterIp, masterPort, masterUser, binlogFileName := getMasterMysqlInfo(bkMysqlPath)
	if err != nil || masterIp == "" || masterPort <= 0 || masterUser == "" {
		errMsg := fmt.Sprintf("Invalid parameter,masterIP=[%v],masterPort=[%v],masterUser=[%v],binlogBkPath=[%v]", masterIp, masterPort, masterUser, binlogBkPath)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	//开启拉取binlog
	serverId, err := startDumpBinlog(masterIp, masterUser, binlogBkPath, binlogFileName, bkMysqlPath, masterPort)
	if err != nil || serverId <= 0 {
		errMsg := fmt.Sprintf("startDumpBinlog failed,err=[%v]", err)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	res := &pb_agent.StartBinlogRes{
		BinlogServerId: int64(serverId),
	}

	return res, nil
}

// 开启binlog备份
func startDumpBinlog(masterIP, masterUser, binlogBkPath, binlogFileName, bkMysqlPath string, masterPort int) (int, error) {
	var serverId int
	//开启拉取binlog进程
	for idx := 0; idx < common.MAXPROCESSCOUNT; idx++ {
		rand.Seed(time.Now().Unix())
		serverId = rand.Intn(65535)
		getBinlogPid := fmt.Sprintf("ps -ef|grep '%v/bin/mysqlbinlog.*--raw.*%v' | grep -v grep | awk '{print $2}'", bkMysqlPath, serverId)
		pid, _, err := common.SyncExecShell(getBinlogPid)
		if err != nil || strings.Trim(pid, " \n\r\t") != "" {
			continue
		} else {
			break
		}
	}

	//判断进程数
	getBinlogPidCount := fmt.Sprintf("ps ux | grep '%v/bin/mysqlbinlog.*--raw' | grep -v grep | wc -l", bkMysqlPath)
	count, _, err := common.SyncExecShell(getBinlogPidCount)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell getBinlogPidCount failed,err=[%v],shell=[%v]", err, getBinlogPidCount)
		logger.Error(errMsg)
		return -1, errors.New(errMsg)
	}
	pidCount, err := strconv.Atoi(strings.Trim(count, " \n\r\t"))
	if err != nil {
		errMsg := fmt.Sprintf("getBinlogPidCount failed,err=[%v],count=[%v]", err, count)
		logger.Error(errMsg)
		return -1, errors.New(errMsg)
	}

	if pidCount > common.MAXPROCESSCOUNT {
		errMsg := fmt.Sprintf("pidCount more than MAXPROCESSCOUNT,pidCount=[%v]", pidCount)
		logger.Error(errMsg)
		return -1, errors.New(errMsg)
	} else {
		//创建目录
		mkShell := fmt.Sprintf("mkdir -p %v", binlogBkPath)
		err = common.SyncExecShellNoResult(mkShell)
		if err != nil {
			errMsg := fmt.Sprintf("SyncExecShell mkShell failed,err=[%v],shell=[%v]", err, mkShell)
			logger.Error(errMsg)
			return -1, errors.New(errMsg)
		}

		//开启binlog拉取数据
		execDumpBinlog := fmt.Sprintf("cd %v && (nohup %v/bin/mysqlbinlog  --no-defaults --raw --read-from-remote-server --stop-never --stop-never-slave-server-id=%v  -h %s -P %v -u %s -p%s %s &)",
			binlogBkPath, bkMysqlPath, serverId, masterIP, masterPort, masterUser, common.MasterPassword, binlogFileName)
		logger.Warn("execDumpBinlog=%v", execDumpBinlog)
		err = common.SyncExecShellNoResult(execDumpBinlog)
		logger.Warn("execDumpBinlog=%v", execDumpBinlog)
		if err != nil {
			logger.Warn("execDumpBinlog=%v", execDumpBinlog)
			errMsg := fmt.Sprintf("SyncExecShell execDumpBinlog failed,err=[%v],shell=[%v]", err, execDumpBinlog)
			logger.Error(errMsg)
			return -1, errors.New(errMsg)
		}
	}

	//判断是否启动成功
	getBinlogPid := fmt.Sprintf("ps -ef|grep '%v/bin/mysqlbinlog.*--raw.*%v' | grep -v grep | awk '{print $2}'", bkMysqlPath, serverId)
	pid, _, err := common.SyncExecShell(getBinlogPid)
	if err != nil || pid == "" {
		logger.Error("SyncExecShell getBinlogPid failed,err=[%v],shell=[%v]", err, getBinlogPid)
		return -1, err
	}
	return serverId, nil
}

// 处理binlog探测任务
func HandleCheckBinlogStatus(taskMsg *pb_agent.MdcAgentAsyncMsg) (*pb_server.CheckBinlogStatusReport, error) {
	binlogServerId := taskMsg.GetCheckBinlogProcessStatus().ServerId
	binlogBkPath := taskMsg.GetCheckBinlogProcessStatus().BkBinlogDir
	bkMysqlPath := taskMsg.GetCheckBinlogProcessStatus().BkMysqlPath
	remoteIp := taskMsg.GetCheckBinlogProcessStatus().RemoteIp
	remotePath := taskMsg.GetCheckBinlogProcessStatus().RemotePath
	if binlogServerId <= 0 || binlogBkPath == "" || bkMysqlPath == "" {
		errMsg := fmt.Sprintf("Invalid parameter,taskMsg=[%v]", taskMsg)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	//判断mysqlbinlog 进程是否存在
	getBinlogPid := fmt.Sprintf("ps -ef|grep \"./mysqlbinlog.*--raw.*%v\" |grep -v grep | awk '{print $2}'", binlogServerId)
	pid, _, err := common.SyncExecShell(getBinlogPid)
	//进程不存在
	if err != nil || pid == "" {
		logger.Error("SyncExecShell get binlog processId failed,err=[%v],shell=[%v]", err, getBinlogPid)
		//获得从库连接主库的账户、密码等信息
		err, masterIp, masterPort, masterUser, binlogFileName := getMasterMysqlInfo(bkMysqlPath)
		if err != nil || masterIp == "" || masterPort <= 0 || masterUser == "" {
			errMsg := fmt.Sprintf("Invalid parameter,masterIP=[%v],masterPort=[%v],masterUser=[%v],binlogBkPath=[%v]", masterIp, masterPort, masterUser, binlogBkPath)
			logger.Error(errMsg)
			return nil, errors.New(errMsg)
		}
		//开启拉取binlog
		serverId, err := startDumpBinlog(masterIp, masterUser, binlogBkPath, binlogFileName, bkMysqlPath, masterPort)
		if err != nil {
			errMsg := fmt.Sprintf("startDumpBinlog failed,err=[%v]", err)
			logger.Error(errMsg)
			return nil, errors.New(errMsg)
		}
		binlogServerId = int64(serverId)
	}
	//处理新产生的binlog文件
	beginUpdateTime, newEndUpdateTime, binlogFileNameResult, err := HandleScpBinlogToRemote(binlogBkPath, remoteIp, remotePath)
	if err != nil {
		errMsg := fmt.Sprintf("HandleScpBinlogToRemote failed,err=[%v],binlogFileNameResult=[%v]", err, binlogFileNameResult)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	response := &pb_server.CheckBinlogStatusReport{
		BinlogServerId:       binlogServerId,
		BeginUpdateTime:      beginUpdateTime,
		EndUpdateTime:        newEndUpdateTime,
		BinlogFileNameResult: binlogFileNameResult,
		RemoteIp:             remoteIp,
		RemotePath:           remotePath,
		BkBinlogPath:         binlogBkPath,
	}

	return response, nil
}

// 处理停止binlog任务
func HandleStopBinlogTask(taskMsg *pb_agent.MdcAgentSyncMsg) error {
	binlogServerId := taskMsg.GetExecStopBinlog().ServerId
	if binlogServerId <= 0 {
		errMsg := fmt.Sprintf("Invalid parameter,binlogServerId=[%v]", binlogServerId)
		logger.Error(errMsg)
		return errors.New(errMsg)
	}
	if err := execStopBinlogDump(binlogServerId); err != nil {
		errMsg := fmt.Sprintf("execStopBinlogDump failed,err=[%v]", err)
		logger.Error(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 执行关闭binlog进程操作
func execStopBinlogDump(serverId int64) error {
	//判断mysqlbinlog 进程是否存在
	getBinlogPid := fmt.Sprintf("ps -ef|grep \"./mysqlbinlog.*--raw.*%v\" | grep -v grep | awk '{print $2}'", serverId)
	binlogProcessId, _, err := common.SyncExecShell(getBinlogPid)
	if err != nil || binlogProcessId == "" {
		errMsg := fmt.Sprintf("SyncExecShell getBinlogPid failed,err=[%v],shell=[%v]", err, getBinlogPid)
		logger.Error(errMsg)
		return errors.New(errMsg)
	}
	//进程存在的话 kill进程
	stopBinlogPid := fmt.Sprintf("kill -9 %v", strings.Trim(binlogProcessId, " \n\r\t"))
	_, _, err = common.SyncExecShell(stopBinlogPid)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell stopBinlogPid failed,err=[%v],shell=[%v]", err, stopBinlogPid)
		logger.Error(errMsg)
		return errors.New(errMsg)
	}
	return nil
}

// 目录是否存在
func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

// 根据配置下载打包的数据，并进行恢复
func RestorePartitionData(taskMsg *pb_agent.MdcAgentAsyncMsg) error {
	var (
		wg         sync.WaitGroup
		once       sync.Once
		errRestore error
	)
	configStr := taskMsg.GetRestorePartitionData().GetRestorePartitionConfig()
	rsConfig := new(global.RestorePartitionConfig)
	err := json.Unmarshal([]byte(configStr), rsConfig)
	if err != nil {
		logger.Error("[RestorePartitionData] can't Unmarshal RestorePartitionConfig. err=[%v] taskId=[%v]", err, taskMsg.TaskId)
		return err
	}
	restoreDir := taskMsg.GetRestorePartitionData().GetRestoreDir()
	if restoreDir == "" {
		err = fmt.Errorf("[RestorePartitionData] restoreDir  can't be empty. taskId=[%v] restoreDir=[%v]", err, restoreDir)
		logger.Error("%v", err)
		return err
	}
	// 1. bos中存储的数据
	// 下载基础var包数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		err = downLoadFromBosAndCreatePath(rsConfig.VarBosUploadPath, restoreDir, rsConfig.VarBosDirMeta.FileCount)
		if err != nil {
			logger.Error("[RestorePartitionData] restore failed. VarBosUploadPath=[%v] "+
				"restoreDir=[%v]", rsConfig.VarBosUploadPath, restoreDir)
			once.Do(func() {
				errRestore = err
			})
		}
	}()
	// 下载其他指定的var包数据
	for _, mapping := range rsConfig.DBMapping {
		wg.Add(1)
		item := mapping
		go func() {
			defer wg.Done()
			err = downLoadFromBosAndCreatePath(item.BosUploadPath, item.DiskPath, item.BosDirMeta.FileCount)
			if err != nil {
				logger.Error("[RestorePartitionData] restore failed. BosUploadPath=[%v] "+
					"DiskPath=[%v]", item.BosUploadPath, item.DiskPath)
				once.Do(func() {
					errRestore = err
				})
			}
		}()
	}
	wg.Wait()
	if errRestore != nil {
		logger.Error("[RestorePartitionData] restore failed. err=[%v]", errRestore)
		return errRestore
	}
	// 2.创建软链
	for _, item := range rsConfig.DBMapping {
		for _, db := range item.DBList {
			newName := fmt.Sprintf("%v/var/%v", restoreDir, db)
			oldName := fmt.Sprintf("%v/var/%v", item.DiskPath, db)
			logger.Info("[RestorePartitionData] create soft link. oldName=[%v] newName=[%v]", oldName, newName)
			err = os.Symlink(oldName, newName)
			if err != nil {
				logger.Error("[RestorePartitionData] create soft link failed. oldName=[%v] newName=[%v] err=[%v]",
					oldName, newName, err)
				return err
			}
		}
	}

	logger.Info("[RestorePartitionData] restore all success. config=[%+v]", taskMsg.GetRestorePartitionData().RestorePartitionConfig)

	return nil
}

// 自动创建目录，并进行文件下载, 进行文件数校验
func downLoadFromBosAndCreatePath(bosPath, localPath string, fileCount int64) error {
	common.Log.Info("[downLoadFromBosAndCreatePath] bosPath=[%v] localPath=[%v]", bosPath, localPath)
	_, file := filepath.Split(strings.TrimRight(bosPath, "/"))
	// 自动创建目录，即使存在也不会报错
	output, status, err := common.SyncExecShell(fmt.Sprintf("mkdir -p %v", localPath))
	if status != 0 || err != nil {
		common.Log.Error("[RestorePartitionData] can't auto create datadir. output=[%v] status=[%v] err=[%v]", output, status, err)
		return err
	}
	localFile := fmt.Sprintf(fmt.Sprintf("%v/%v", localPath, file))
	// 下载文件
	err = common.Do(
		func() error {
			return bos.BosCmdSyncDirFromBos(common.Config.DbaBosBucket, bosPath, localFile)
		},
		5,
		5*time.Second,
	)
	if err != nil {
		common.Log.Error("[RestorePartitionData] DownLoadDataFromBos  failed. bosPath=[%v] localPath=[%v] "+
			"err=[%v]", bosPath, localFile, err)
		return err
	}
	// 重命名文件
	if file != "var" {
		mvShell := fmt.Sprintf("cd %v && mv %v var", localPath, file)
		output, status, err = common.SyncExecShell(mvShell)
		if status != 0 || err != nil {
			common.Log.Error("[RestorePartitionData] can't auto create datadir. output=[%v] status=[%v] err=[%v]", output, status, err)
			return err
		}
	}
	// 检验文件数量
	filePath := fmt.Sprintf("%v/var", localPath)
	actualCount, err := bos.GetLocalDirFileCount(filePath)
	if err != nil || actualCount != fileCount {
		err = fmt.Errorf("[RestorePartitionData] check var file count failed. actualCount=[%v] expectCount=[%v] filePath=[%v] err=[%v]",
			actualCount, fileCount, filePath, err)
		return err
	}
	common.Log.Info("[RestorePartitionData] check var file count success. actualCount=[%v] expectCount=[%v] filePath=[%v] err=[%v]",
		actualCount, fileCount, filePath, err)

	return nil
}

// 处理备份用户权限  修改返回结构
func HandlExecSqlBackup(taskMsg *pb_agent.MdcAgentAsyncMsg) (*pb_server.ExecAccountBackupReport, error) {

	bkMysqlPath := taskMsg.GetExecAccountPermissionBackup().BkMysqlPath
	bkRemoteIp := taskMsg.GetExecAccountPermissionBackup().BkRemoteIp
	bkFileName := taskMsg.GetExecAccountPermissionBackup().Bkfilename
	// 需要一个大磁盘路径
	bkRemotePath := taskMsg.GetExecAccountPermissionBackup().BkRemotepath

	//创建目录
	filedir, _ := filepath.Split(bkMysqlPath)
	// sql文件存放目录
	bkfiledir := fmt.Sprint("%v/%v", filedir, common.AccountBkDir)
	if err := os.Mkdir(bkfiledir, 0755); err != nil {
		errMsg := fmt.Sprintf("创建目录失败", err)
		common.Log.Error("[HandlExecxtra] accountPersionmiss backup failed. errMsg=[%v] output=[%v]", errMsg)
		return nil, errors.New(errMsg)
	}
	//创建文件
	filePath := fmt.Sprintf("%s/%s", bkfiledir, bkFileName)
	file, err := os.OpenFile(filePath, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		errMsg := fmt.Sprintf("创建文件失败:", err)
		common.Log.Error("[HandlExecxtra] accountPersionmiss backup failed. errMsg=[%v] output=[%v]", errMsg)
		return nil, errors.New(errMsg)
	}

	//生成用户权限备份
	getMysqlUserSql := "%s/bin/mysql --defaults-file=%s/etc/user.root.cnf -BNe \"select concat('show create user ',user,'@','\\'',host,'\\'',';show grants for ',user,'@','\\'',host,'\\'',';') from mysql.user where user not in ('mysql.session','mysql.sys','root','admin','rootsavior')\" | while read line;do %s/bin/mysql --defaults-file=%s/etc/user.root.cnf -NBe \"${line}\";done | while read grant;do echo \"${grant};\";done"
	getOnlinePrivilegesCmd := fmt.Sprintf(getMysqlUserSql, bkMysqlPath, bkMysqlPath, bkMysqlPath, bkMysqlPath)
	grantRes, _, err := common.SyncExecShell(getOnlinePrivilegesCmd)
	if err != nil {
		errMsg := fmt.Sprintf("SyncExecShell replicationStart failed. err=[%v] shell=[%v]", err, getOnlinePrivilegesCmd)
		common.Log.Error("[HandlExecxtra] SqlBackup failed. errMsg=[%v] output=[%v]", errMsg)
		return nil, errors.New(errMsg)
	}

	//修改create user语句为 create user if not exists
	sqlArray := strings.Split(grantRes, "\n")
	for i := 0; i < len(sqlArray); i++ {
		if strings.HasPrefix(sqlArray[i], "CREATE USER") {
			sqlArray[i] = strings.Replace(sqlArray[i], "CREATE USER", "CREATE USER IF NOT EXISTS", 1)
		}
	}
	grantRes = strings.Join(sqlArray, "\n")
	// 将授权结果进行写文件操作
	_, err = file.Write([]byte(grantRes))
	if err != nil {
		errMsg := fmt.Sprintf("write sql file failed, err=[%v]", err)
		common.Log.Error(errMsg)
		return nil, errors.New(errMsg)
	}
	//通过对比完成时间来确定当天最新备份
	endTime := time.Now().Format(common.TIME_FORMAT)
	//时间戳
	//TimeStamp := time.Now().Unix()
	remoteFile := fmt.Sprintf("%v/%v", bkRemotePath, bkFileName)

	//拷贝文件到大磁盘
	if err = common.ScpBinlogToRemoteAndDelete(bkRemoteIp, bkRemotePath, filePath); err != nil {
		errMsg := fmt.Sprintf("HandleScpBinlogToRemote failed,err=[%v]", err)
		common.Log.Warn(errMsg)
		return nil, err
	}

	//汇报任务情况
	mdcResMsg := &pb_server.ExecAccountBackupReport{
		EndTime: endTime,
		// 远程大磁盘存储的文件路径
		RemotePath: remoteFile,
		//TimeStamp:  TimeStamp,
	}

	return mdcResMsg, nil
}

// 处理用户备份上传到dbabos
func HandleSqlBackupUpLoadDbaBos(taskMsg *pb_agent.MdcAgentAsyncMsg) (*pb_server.AccountBackupUploadDBABOSReport, error) {

	filePath := taskMsg.GetAccountBackupUploadDbaBos().DataDir
	bosPath := taskMsg.GetAccountBackupUploadDbaBos().DbaBosDir
	_, bkFileName := filepath.Split(filePath)
	bosPath = fmt.Sprintf("%v/%v", bosPath, bkFileName)
	logger.Debug("[HandleSqlBackupUpLoadDbaBos] filePath=[%v] bosPath=[%v]", filePath, bosPath)
	if filePath == "" || bosPath == "" {
		errMsg := fmt.Sprintf("Invalid parameter,filePath=[%v],bosPath=[%v]", filePath, bosPath)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	isSuccess, err := bos.BlockUploadToBos(filePath, bosPath)
	if !isSuccess || err != nil {
		errMsg := fmt.Sprintf("BlockUploadToBos failed. err =[%v] binlogPath=[%v] bosPath=[%v]", err, filePath, bosPath)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	mdcResMsg := &pb_server.AccountBackupUploadDBABOSReport{
		BosPathFile: bosPath,
		BosBucket:   common.Config.DbaBosBucket,
	}

	return mdcResMsg, err
}
