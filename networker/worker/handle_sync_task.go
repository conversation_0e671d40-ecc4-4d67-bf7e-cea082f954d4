package worker

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"dt-common/global"
	pb_agent "dt-common/protobuf/mdc-agent"

	"mdc-agent/common"
)

// HandleGrantPrivileges 处理添加授权请求
func HandleGrantPrivileges(taskMsg *pb_agent.MdcAgentSyncMsg) (err error) {
	configStr := taskMsg.GetGrantPrivileges().GrantPrivilegesConfig
	var config global.GrantPrivilegesConfig
	err = json.Unmarshal([]byte(configStr), &config)
	if err != nil {
		return err
	}

	for _, ip := range config.IPList {
		rand.Seed(time.Now().UnixNano())
		fileName := fmt.Sprintf("dbs_grant_%v_%v.sql", time.Now().UnixNano(), rand.Intn(100))
		shell := fmt.Sprintf(`mkdir -p grant && cd grant && echo 'set sql_log_bin=0;'> %v && export base_dir=%v &&  ${base_dir}/bin/mysql --defaults-file=${base_dir}/etc/user.root.cnf -BNe "select concat('show create user ',user,'@','\'',host,'\'',';show grants for ',user,'@','\'',host,'\'',';') from mysql.user where user not in ('mysql.session','mysql.sys','root','admin','mysqlsync')  and host not in ('%%','localhost') group by user"|while read line;do ${base_dir}/bin/mysql --defaults-file=${base_dir}/etc/user.root.cnf -NBe "${line}";done|while read grant;do echo "${grant}"";";done|sed -E 's/([0-9]{1,3}\.){3}[0-9]{1,3}/%v/g' >> %v && 
cat %v | ${base_dir}/bin/mysql --defaults-file=${base_dir}/etc/user.root.cnf -f `,
			fileName, config.BaseDir, ip, fileName, fileName)
		output, status, err := common.SyncExecShell(shell)
		if err != nil || status != 0 {
			common.Log.Error("fail to HandleGrantPrivileges. output=[%v] status=[%v] err=[%v]",
				output, status, err)
			return err
		}
	}

	return nil
}
