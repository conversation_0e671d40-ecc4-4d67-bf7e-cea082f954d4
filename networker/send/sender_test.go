package send

import (
	"testing"

	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/smartystreets/goconvey/convey"
)

func TestSendInitMsgToServer(t *testing.T) {
	syncMsg := pb_server.MdcAgentReportMsg{
		MsgType: &pb_server.MdcAgentReportMsg_ExecXtrabkReport{
			ExecXtrabkReport: &pb_server.ExecXtrabkReport{
			},
		},
	}

	err := SendReportMsgToServer(&syncMsg)
	Convey("send proxy init message to proxy success", t, func() {
		So(err, ShouldBeNil)
	})
}
