/* Copyright 2019 Du Xiaoman Financial Inc. All Rights Reserved. */
/* constants.go - the constants of the server  */
/*
modification history
--------------------
2020/11/27,g<PERSON><PERSON><PERSON><PERSON>, create
/*
DESCRIPTION
This file contains the function for rpc sender
*/
package send

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"

	pb_server "dt-common/protobuf/mdc-server"
	"mdc-agent/common"
	"mdc-agent/library/logger"
)

// 向mdc-server发送任务处理结果
func SendReportMsgToServer(data *pb_server.MdcAgentReportMsg) error {
	logger.Info("[SendReportMsgToServer] msgType=[%+v] baseMessage=[%+v]", data.GetMsgType(), *data.GetBaseMessage())
	for _, ip := range common.MdcServerIpList.IpList {
		addr := fmt.Sprintf("%s:%d", ip, common.Config.MdcRpcServer.Port)
		conn, err := InitServerConn(addr)
		if nil != err {
			logger.Warn("connect to Mdc server failed, taskId=[%v], err=[%v], addr=[%v]", data.Taskid, err, addr)
			continue
		}
		defer conn.Close()
		logger.Debug("Send ms task result to server. taskId=[%v], addr=[%v], dsTaskInfo=[%v]", data.Taskid, addr, data)
		client := pb_server.NewMDCMsgReceiverClient(conn)
		ctx, cancel := context.WithTimeout(context.Background(), time.Second*time.Duration(common.Config.ServerRWTimeout))
		defer cancel()
		res, err := client.HandleMdcAgentReportMsg(ctx, data)
		if err != nil {
			logger.Warn("Send data to mdc server failed, taskId=[%v], err=[%v]", data.Taskid, err)
		} else {
			logger.Debug("Send data to mdc server succeed, taskId=[%v], response=%v", data.Taskid, res)
		}
	}
	return nil
}

// 初始化mdc-server连接
func InitServerConn(addr string) (*grpc.ClientConn, error) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*time.Duration(common.Config.ServerConnTimeout))
	conn, err := grpc.DialContext(ctx, addr, grpc.WithBlock(), grpc.WithInsecure(), grpc.WithKeepaliveParams(keepalive.ClientParameters{
		Time:                time.Duration(common.Config.RpcKeepalive.RpcClientParam.Time) * time.Second,
		Timeout:             time.Duration(common.Config.RpcKeepalive.RpcClientParam.TimeOut) * time.Second,
		PermitWithoutStream: common.Config.RpcKeepalive.RpcClientParam.PermitWithOutStream}))
	if err != nil {
		cancel()
	}
	return conn, err
}
