package main

import (
	"fmt"
	"runtime"

	_ "github.com/go-sql-driver/mysql"

	"mdc-agent/env"
	"mdc-agent/library/logger"
	"mdc-agent/library/xtrabackup"
)

func main() {
	// 控制main使用cpu的总数,只用一颗cpu
	runtime.GOMAXPROCS(1)

	// 配置初始化
	env.Init()
	logger.Info("initialization successful, server starting...")

	// 备份
	opts := xtrabackup.BackupOptions{
		Cluster:              "test",
		DefaultFile:          "/home/<USER>/mysql/etc/my.cnf",
		Socket:               "/home/<USER>/mysql/tmp/mysql.sock",
		User:                 "root",
		Password:             "_Y5%C2wncJC6b^frHdiEKw*kn05VNN",
		UseMemory:            "8G",
		CompressThreads:      4,
		Parallel:             16,
		Type:                 "full", // 全备
		Throttle:             500,
		KillLongQueryType:    "select",
		KillLongQueryTimeout: 20,
		Stream:               true,
		RemoteHost:           "work@************",
		RemotePath:           "/home/<USER>/backups",
	}

	ret, err := xtrabackup.StartBackup(&opts)
	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Printf("File: %s, StartTime: %s, EndTime: %s, LSNFrom: %s, LSNTo: %s\n", ret.FilePath, ret.StartTime, ret.EndTime, ret.LSNFrom, ret.LSNTo)
}
