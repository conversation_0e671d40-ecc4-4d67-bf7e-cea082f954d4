#!/bin/bash
export GOROOT=/home/<USER>/soft/go_1.16/
export PATH=${GOROOT}/bin:${PATH}
go env -w GO111MODULE="on"
go env -w GOPROXY=http://goproxy.duxiaoman-int.com/nexus/repository/goproxy.cn/
go env -w GOPRIVATE="*.duxiaoman-int.com"
go env -w GONOPROXY="**.duxiaoman-int.com**"
go env -w GONOSUMDB="*"
go env -w "GOFLAGS"="-mod=mod"
wget -O output.tar.gz  "http://irep.build.duxiaoman-int.com/product/v3/download/release/dxm/dba/supervise/1.0.0.2/output.tgz" && tar -zxvf output.tar.gz && mv output/bin/supervise ./ && rm -rf output.tar.gz