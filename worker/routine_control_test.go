package worker

import (
	"sync"
	"testing"
	"time"

	pb_agent "dt-common/protobuf/mdc-agent"
	pb_server "dt-common/protobuf/mdc-server"
	. "github.com/smartystreets/goconvey/convey"

	"mdc-agent/common"
)

func TestStartWorker(t *testing.T) {
	//主协程等待变量
	waitgroup := new(sync.WaitGroup)
	//启动监听
	waitgroup.Add(1)
	<PERSON>vey("StartWorker : 启动woker失败", t, func() {
		go StartWorker(1, 1, waitgroup)
	})
	agentMsgFail := &pb_agent.MdcAgentAsyncMsg{}
	common.AgentMsgChan <- agentMsgFail
	waitgroup.Add(1)
	Convey("StartWorker : 启动woker成功", t, func() {
		go StartWorker(1, 1, waitgroup)
	})
	agentMsg := pb_agent.MdcAgentAsyncMsg{
		MdcAgentTaskType: pb_server.MdcAgentTaskType_START_XTRA,
		BaseMessage: &pb_server.MdcBaseMessage{
			ClusterName: "test",
			ClusterId:   1,
			NodeId:      1,
		},
		MsgType: &pb_agent.MdcAgentAsyncMsg_StartXtrabkExecute{
		},
	}
	common.AgentMsgChan <- &agentMsg
	waitgroup.Add(1)
	Convey("StartWorker : 启动woker成功", t, func() {
		go StartWorker(1, 1, waitgroup)
	})
	time.Sleep(time.Second * 2)
	Convey("StopWorker : 关闭woker", t, func() {
		StopWorker()
	})
}
