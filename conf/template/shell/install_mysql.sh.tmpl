#!/bin/bash

#临时存放安装文件的目录
tmp_base_dir={{.TmpBaseDir}}_{{.ServerId}}
#mysql目录
base_dir={{.BaseDir}}
data_dir={{.DataDir}}
#port
port={{.Port}}
#server-id
server_id={{.ServerId}}
#download_url
download_url={{.DownloadUrl}}

#log函数
#$1可以取INFO/EROOR等...
function log(){
    if (( $# == 2 ));then
        case $1 in
            INFO )
                echo "[INFO] `date +"%Y-%m-%d %H:%M:%S"` $2"
                ;;
            ERROR )
                echo "[ERROR] `date +"%Y-%m-%d %H:%M:%S"` $2"
                exit 1
                ;;
             * )
                echo "[$1] `date +"%Y-%m-%d %H:%M:%S"` $2"
                ;;
        esac
    else
        case $1 in
            0 )
                echo "[INFO] `date +"%Y-%m-%d %H:%M:%S"` $2"
                ;;
            * )
                echo "[ERROR] `date +"%Y-%m-%d %H:%M:%S"` $3"
                exit 1
                ;;
        esac
    fi
}

#预检查
function precheck(){
  if [[ -e ${base_dir} ]]; then
    #存在basedir,报错退出
    log ERROR "basedir exist, this operation may overwrite it , exit 1"
  else
    log INFO "check pass, base_dir: ${base_dir} not exist"
  fi

#  port_status=$((netstat -tunl | grep -w ${port} | wc -l ) 2>/dev/null)
#  #端口计数非0,说明存在端口占用
#  if [[ ${port_status} != 0 ]]; then
#    log ERROR "port: ${port} is listening"
#  else
#    log INFO "check pass, port: ${port} not listening"
#  fi

}


#安装函数
function install_mysql(){


    log INFO "start download mysql.tar.gz"
    #下载软件包
    mkdir -p  ${tmp_base_dir}
    log $? "create tmp_mysql dir ${tmp_base_dir} success" "create mysql dir ${tmp_base_dir} failed"
    cd ${tmp_base_dir} && $(download_url) && tar -zxf $(basename ${download_url})
    log $?  "download ${download_url}  and decompress success" "download ${download_url}  or decompress failed"

    #清理安装包
    rm $(basename ${download_url})
    log INFO "clean ${download_url}"

    #拷贝配置文件,并替换server-id
    mv  ${data_dir}/*.cnf mysql/etc/  &&  sed -i "/^server-id/c server-id    = ${server_id}" mysql/etc/mysqld.cnf
    log $? "copy conf files succesqs" "copy conf files failed"

    #创建存放mysql的基础目录
    #mkdir -p $(dirname ${base_dir})
    #log $? "create mysql dir $(dirname ${base_dir}) success" "create mysql dir $(dirname ${base_dir}) failed"

    #move实例,将临时mysql目录移动到mysql目录
    mv  ${tmp_base_dir}/mysql ${base_dir}
    log $? "move mysql dir success" "move mysql dir failed"

    #启动mysql实例。bin/mysqld_safe --defaults-file=etc/my.cnf &
    cd ${base_dir} && (bin/mysqld_safe --defaults-file=etc/my.cnf &)  >/dev/null  2>&1
    log $? "start mysql...." "start mysql failed"

    #死循环时间,最多等待5*50s
    deadtime=50
    until [[ -e ${base_dir}/tmp/mysql.pid ]]
    do
        sleep 5
        deadtime=$(( deadtime - 1 ))
        if (( ${deadtime} == 0 )); then
          log ERROR "wait for mysql start timeout,exit"
        fi
        log INFO "wait for mysql start...."
    done

    status=$(cd ${base_dir} &&  bin/mysql --defaults-file=etc/user.root.cnf -NBe "select 0;")
    #判断是否启动
    if [[ X${status} != X0 ]]; then
        log ERROR "detect mysql failed,can not select 0;"
    else
        log INFO "detect mysql  success "
    fi
    #清理临时目录
    rm -rf ${tmp_base_dir}
    log $? "clean tmp_base_dir  ${tmp_base_dir} success" "clean tmp_base_dir ${tmp_base_dir} failed"

}


#main 函数执行入口
function main(){
    precheck
    install_mysql
}

main


