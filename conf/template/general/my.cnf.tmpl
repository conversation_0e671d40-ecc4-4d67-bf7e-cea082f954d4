[client]
port = {{.Port}}
socket = {{.BaseDir}}/tmp/mysql.sock

[mysqld]
core-file
!include {{.BaseDir}}/etc/mysqld.cnf
port = {{.Port}}
socket = {{.BaseDir}}/tmp/mysql.sock
pid-file = {{.BaseDir}}/tmp/mysql.pid
basedir = {{.BaseDir}}
datadir = {{.DataDir}}/var

# character
character-set-server = utf8mb4
collation-server = utf8mb4_general_ci


# tmp dir settings
tmpdir = {{.BaseDir}}/tmp
slave-load-tmpdir = {{.BaseDir}}/tmp
secure_file_priv = "" # null

# skip options
skip-name-resolve
skip-symbolic-links
skip-external-locking
skip-slave-start

#sysdate-is-now
#5.7

innodb_page_cleaners = 4
innodb_undo_log_truncate = 1
innodb_undo_tablespaces=2
#slave parrllel config
#slave-parallel-workers=4
#slave-parallel-type=LOGICAL_CLOCK

sql_mode=NO_ENGINE_SUBSTITUTION

#GTID
gtid-mode = on
enforce-gtid-consistency = 1
# res settings
back_log = 1000
max_connections = 4000
max_connect_errors = 10000

#lossless-replication
plugin_dir = {{.BaseDir}}/lib/plugin
plugin_load = "rpl_semi_sync_master=semisync_master.so;rpl_semi_sync_slave=semisync_slave.so"
rpl_semi_sync_master_wait_point = AFTER_SYNC

connect-timeout = 5
wait-timeout = 28800
interactive-timeout = 28800
slave-net-timeout = 600
net_read_timeout = 30
net_write_timeout = 60
net_retry_count = 10
net_buffer_length = 16384
max_allowed_packet = 64M

#
thread_stack = 192K
thread_cache_size = 20

# qcache settings
query_cache_type = 0

# default settings
# time zone
default-time-zone = system
log_timestamps = SYSTEM
default-storage-engine = InnoDB

# tmp & heap
tmp_table_size = 512M
max_heap_table_size = 512M

log-bin = mysql-bin
log-bin-index = mysql-bin.index
max_binlog_size = 1G
sync-binlog = 1000
binlog-format = ROW
relay_log_recovery = 1
master_info_repository=TABLE
relay_log_info_repository=TABLE
relay-log = relay-log
relay-log-index = relay-log.index
sync_relay_log = 1000
max_relay_log_size = 1G

# warning & error log
log-warnings = 1
log-error = {{.BaseDir}}/log/mysql.err

# slow query log
long-query-time = 1
slow_query_log = 1
slow_query_log_file = {{.BaseDir}}/log/slow.log
log_queries_not_using_indexes   =   1
log_throttle_queries_not_using_indexes  =   0
min_examined_row_limit  =   10000
#log-queries-not-using-indexes
# general query log
general_log = 1
general_log_file = {{.BaseDir}}/log/mysql.log

table_open_cache = 30000
table_definition_cache = 30000


# if use auto-ex, set to 0
relay-log-purge = 1

# max binlog keeps days
expire_logs_days = 7
binlog_cache_size = 1M

# replication
replicate-wild-ignore-table = mysql.%
replicate-wild-ignore-table = test.%
# slave_skip_errors=all

key_buffer_size = 256M
sort_buffer_size = 2M
read_buffer_size = 2M
join_buffer_size = 8M
read_rnd_buffer_size = 8M


bulk_insert_buffer_size = 64M
myisam_sort_buffer_size = 64M
myisam_max_sort_file_size = 10G
myisam_repair_threads = 1
myisam_recover_options = OFF

transaction_isolation = REPEATABLE-READ

#skip-innodb
innodb_file_per_table=on
innodb_file_format = Barracuda

#innodb_open_files = 2048
innodb_buffer_pool_size = 4G
innodb_data_home_dir = {{.DataDir}}/var
innodb_data_file_path = ibdata1:1G:autoextend
innodb_temp_data_file_path = ibtmp1:12M:autoextend
innodb_read_io_threads = 20
innodb_write_io_threads = 20
innodb_thread_concurrency = 32
innodb_use_native_aio = 1
innodb_flush_log_at_trx_commit = 1
innodb_flush_neighbors = 0
innodb_flush_method = O_DIRECT
innodb_io_capacity = 4000
innodb_io_capacity_max = 7000
innodb_print_all_deadlocks = 1

# redolog setting
innodb_log_buffer_size = 8M
innodb_log_file_size = 1900M
innodb_log_files_in_group = 2
innodb_log_group_home_dir = {{.DataDir}}/var

innodb_max_dirty_pages_pct = 90
innodb_lock_wait_timeout = 50
#innodb-adaptive-hash-index = 0
#innodb_compression_failure_threshold_pct = 0
#autocommit = 0

[mysqldump]
quick
max_allowed_packet = 64M

[mysql]
disable-auto-rehash
connect-timeout = 3
default-character-set = utf8mb4

[myisamchk]
key_buffer_size = 256M
sort_buffer_size = 256M
read_buffer_size = 2M
write_buffer_size = 2M

[mysqlhotcopy]
interactive-timeout
